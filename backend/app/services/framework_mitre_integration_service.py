"""
Framework-MITRE ATT&CK Integration Service
Provides services for mapping framework controls to MITRE techniques and analysis
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from uuid import UUID
import re
from collections import defaultdict

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, text

from app.db.models.cybersecurity_frameworks import (
    CybersecurityFramework,
    FrameworkControl,
    ControlImplementation
)
from app.db.models.mitre import (
    MitreTechnique,
    MitreTactic,
    MitreGroup
)
from app.db.models.framework_mitre_mappings import (
    FrameworkMitreMapping,
    FrameworkTacticCoverage,
    AttackPathControlMapping,
    ControlThreatIntelligence,
    FrameworkComplianceMetrics,
    MappingType,
    MappingConfidence
)

logger = logging.getLogger(__name__)


class FrameworkMitreIntegrationService:
    """Service for integrating cybersecurity frameworks with MITRE ATT&CK"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def create_control_technique_mapping(
        self,
        control_id: UUID,
        technique_id: UUID,
        mapping_type: MappingType,
        confidence_level: MappingConfidence,
        relevance_score: float,
        rationale: str = None,
        user_id: UUID = None
    ) -> FrameworkMitreMapping:
        """Create a mapping between a framework control and MITRE technique"""
        
        # Check if mapping already exists
        existing_mapping = self.db.query(FrameworkMitreMapping).filter(
            and_(
                FrameworkMitreMapping.control_id == control_id,
                FrameworkMitreMapping.technique_id == technique_id,
                FrameworkMitreMapping.deleted_at.is_(None)
            )
        ).first()
        
        if existing_mapping:
            # Update existing mapping
            existing_mapping.mapping_type = mapping_type.value
            existing_mapping.confidence_level = confidence_level.value
            existing_mapping.relevance_score = relevance_score
            existing_mapping.rationale = rationale
            existing_mapping.updated_by = user_id
            existing_mapping.updated_at = datetime.utcnow()
            
            self.db.commit()
            return existing_mapping
        
        # Create new mapping
        mapping = FrameworkMitreMapping(
            control_id=control_id,
            technique_id=technique_id,
            mapping_type=mapping_type.value,
            confidence_level=confidence_level.value,
            relevance_score=relevance_score,
            rationale=rationale,
            created_by=user_id,
            updated_by=user_id
        )
        
        self.db.add(mapping)
        self.db.commit()
        self.db.refresh(mapping)
        
        logger.info(f"Created control-technique mapping: {control_id} -> {technique_id}")
        
        return mapping
    
    async def auto_generate_control_mappings(
        self,
        framework_id: UUID,
        confidence_threshold: float = 0.6,
        user_id: UUID = None
    ) -> List[FrameworkMitreMapping]:
        """Automatically generate control-technique mappings using ML/NLP"""
        
        # Get all controls for the framework
        controls = self.db.query(FrameworkControl).filter(
            and_(
                FrameworkControl.framework_id == framework_id,
                FrameworkControl.deleted_at.is_(None)
            )
        ).all()
        
        # Get all MITRE techniques
        techniques = self.db.query(MitreTechnique).filter(
            MitreTechnique.deleted_at.is_(None)
        ).all()
        
        mappings = []
        
        for control in controls:
            # Find relevant techniques for this control
            relevant_techniques = await self._find_relevant_techniques(
                control, techniques, confidence_threshold
            )
            
            for technique, relevance_data in relevant_techniques:
                mapping = await self.create_control_technique_mapping(
                    control_id=control.id,
                    technique_id=technique.id,
                    mapping_type=MappingType(relevance_data['mapping_type']),
                    confidence_level=MappingConfidence.MEDIUM,  # Auto-generated mappings start as medium confidence
                    relevance_score=relevance_data['relevance_score'],
                    rationale=relevance_data['rationale'],
                    user_id=user_id
                )
                mappings.append(mapping)
        
        logger.info(f"Auto-generated {len(mappings)} control-technique mappings for framework {framework_id}")
        
        return mappings
    
    async def _find_relevant_techniques(
        self,
        control: FrameworkControl,
        techniques: List[MitreTechnique],
        confidence_threshold: float
    ) -> List[Tuple[MitreTechnique, Dict[str, Any]]]:
        """Find MITRE techniques relevant to a framework control"""
        
        relevant_techniques = []
        
        # Prepare control text for analysis
        control_text = f"{control.name} {control.description or ''} {control.objective or ''}"
        control_keywords = self._extract_keywords(control_text.lower())
        
        for technique in techniques:
            # Prepare technique text for analysis
            technique_text = f"{technique.name} {technique.description or ''}"
            technique_keywords = self._extract_keywords(technique_text.lower())
            
            # Calculate relevance score
            relevance_score = self._calculate_text_similarity(control_keywords, technique_keywords)
            
            # Determine mapping type based on control type and technique characteristics
            mapping_type = self._determine_mapping_type(control, technique)
            
            # Apply domain-specific rules
            domain_boost = self._apply_domain_rules(control, technique)
            relevance_score = min(relevance_score + domain_boost, 1.0)
            
            if relevance_score >= confidence_threshold:
                rationale = self._generate_mapping_rationale(control, technique, relevance_score)
                
                relevant_techniques.append((technique, {
                    'relevance_score': relevance_score,
                    'mapping_type': mapping_type,
                    'rationale': rationale
                }))
        
        # Sort by relevance score
        relevant_techniques.sort(key=lambda x: x[1]['relevance_score'], reverse=True)
        
        return relevant_techniques[:5]  # Return top 5 matches
    
    def _extract_keywords(self, text: str) -> set:
        """Extract relevant keywords from text"""
        
        # Security-specific keywords that are important for mapping
        security_keywords = {
            'access', 'authentication', 'authorization', 'encryption', 'monitoring',
            'logging', 'backup', 'recovery', 'incident', 'vulnerability', 'patch',
            'configuration', 'network', 'firewall', 'antivirus', 'malware', 'audit',
            'compliance', 'risk', 'security', 'privacy', 'data', 'system', 'user',
            'password', 'credential', 'certificate', 'key', 'token', 'session',
            'endpoint', 'server', 'database', 'application', 'service', 'process',
            'file', 'directory', 'registry', 'policy', 'procedure', 'control',
            'detection', 'prevention', 'response', 'analysis', 'forensics'
        }
        
        # Extract words and filter for security keywords
        words = set(re.findall(r'\b\w+\b', text.lower()))
        return words.intersection(security_keywords)
    
    def _calculate_text_similarity(self, keywords1: set, keywords2: set) -> float:
        """Calculate similarity between two sets of keywords"""
        
        if not keywords1 or not keywords2:
            return 0.0
        
        intersection = len(keywords1.intersection(keywords2))
        union = len(keywords1.union(keywords2))
        
        # Jaccard similarity
        jaccard = intersection / union if union > 0 else 0.0
        
        # Boost score if there are many common keywords
        if intersection >= 3:
            jaccard += 0.1
        
        return min(jaccard, 1.0)
    
    def _determine_mapping_type(self, control: FrameworkControl, technique: MitreTechnique) -> str:
        """Determine the type of mapping based on control and technique characteristics"""
        
        control_type = control.control_type or ""
        control_text = f"{control.name} {control.description or ''}".lower()
        technique_text = f"{technique.name} {technique.description or ''}".lower()
        
        # Preventive controls
        if any(keyword in control_text for keyword in ['prevent', 'block', 'restrict', 'deny', 'disable']):
            return MappingType.PREVENTIVE.value
        
        # Detective controls
        if any(keyword in control_text for keyword in ['detect', 'monitor', 'log', 'alert', 'audit']):
            return MappingType.DETECTIVE.value
        
        # Corrective controls
        if any(keyword in control_text for keyword in ['respond', 'remediate', 'recover', 'restore']):
            return MappingType.CORRECTIVE.value
        
        # Default based on control type
        if control_type in ['preventive', 'protective']:
            return MappingType.PREVENTIVE.value
        elif control_type in ['detective', 'monitoring']:
            return MappingType.DETECTIVE.value
        elif control_type in ['corrective', 'responsive']:
            return MappingType.CORRECTIVE.value
        
        return MappingType.PREVENTIVE.value  # Default
    
    def _apply_domain_rules(self, control: FrameworkControl, technique: MitreTechnique) -> float:
        """Apply domain-specific rules to boost relevance scores"""
        
        boost = 0.0
        
        control_text = f"{control.name} {control.description or ''}".lower()
        technique_text = f"{technique.name} {technique.description or ''}".lower()
        
        # Access control mappings
        if 'access' in control_text and any(t in technique_text for t in ['account', 'credential', 'privilege']):
            boost += 0.2
        
        # Network security mappings
        if 'network' in control_text and any(t in technique_text for t in ['network', 'remote', 'lateral']):
            boost += 0.2
        
        # Endpoint security mappings
        if any(e in control_text for e in ['endpoint', 'workstation', 'device']) and \
           any(t in technique_text for t in ['execution', 'persistence', 'defense']):
            boost += 0.2
        
        # Data protection mappings
        if any(d in control_text for d in ['data', 'information', 'encryption']) and \
           any(t in technique_text for t in ['collection', 'exfiltration', 'discovery']):
            boost += 0.2
        
        return boost
    
    def _generate_mapping_rationale(
        self, 
        control: FrameworkControl, 
        technique: MitreTechnique, 
        relevance_score: float
    ) -> str:
        """Generate human-readable rationale for the mapping"""
        
        control_keywords = self._extract_keywords(f"{control.name} {control.description or ''}".lower())
        technique_keywords = self._extract_keywords(f"{technique.name} {technique.description or ''}".lower())
        
        common_keywords = control_keywords.intersection(technique_keywords)
        
        rationale_parts = []
        
        if common_keywords:
            rationale_parts.append(f"Common security concepts: {', '.join(sorted(common_keywords)[:3])}")
        
        if control.control_type:
            rationale_parts.append(f"Control type: {control.control_type}")
        
        if technique.tactic:
            rationale_parts.append(f"MITRE tactic: {technique.tactic}")
        
        rationale_parts.append(f"Relevance score: {relevance_score:.2f}")
        
        return "; ".join(rationale_parts)
    
    async def analyze_framework_mitre_coverage(
        self,
        framework_id: UUID,
        organization_id: UUID = None
    ) -> Dict[str, Any]:
        """Analyze MITRE ATT&CK coverage for a framework"""
        
        # Get framework
        framework = self.db.query(CybersecurityFramework).filter(
            and_(
                CybersecurityFramework.id == framework_id,
                CybersecurityFramework.deleted_at.is_(None)
            )
        ).first()
        
        if not framework:
            raise ValueError("Framework not found")
        
        # Get all mappings for this framework
        mappings_query = self.db.query(FrameworkMitreMapping).join(FrameworkControl).filter(
            and_(
                FrameworkControl.framework_id == framework_id,
                FrameworkMitreMapping.deleted_at.is_(None),
                FrameworkControl.deleted_at.is_(None)
            )
        )
        
        mappings = mappings_query.all()
        
        # Get all MITRE tactics and techniques
        tactics = self.db.query(MitreTactic).filter(MitreTactic.deleted_at.is_(None)).all()
        all_techniques = self.db.query(MitreTechnique).filter(MitreTechnique.deleted_at.is_(None)).all()
        
        # Analyze coverage by tactic
        tactic_coverage = {}
        
        for tactic in tactics:
            tactic_techniques = [t for t in all_techniques if t.tactic == tactic.name]
            covered_techniques = []
            
            for mapping in mappings:
                if mapping.technique.tactic == tactic.name:
                    covered_techniques.append(mapping.technique)
            
            coverage_percentage = (len(covered_techniques) / len(tactic_techniques) * 100) if tactic_techniques else 0
            
            tactic_coverage[tactic.name] = {
                'tactic_id': str(tactic.id),
                'total_techniques': len(tactic_techniques),
                'covered_techniques': len(covered_techniques),
                'coverage_percentage': coverage_percentage,
                'covered_technique_ids': [str(t.id) for t in covered_techniques],
                'gaps': [
                    {
                        'technique_id': str(t.id),
                        'technique_name': t.name,
                        'technique_mitre_id': t.technique_id
                    }
                    for t in tactic_techniques if t not in covered_techniques
                ]
            }
        
        # Overall coverage statistics
        total_techniques = len(all_techniques)
        covered_techniques = len(set(mapping.technique_id for mapping in mappings))
        overall_coverage = (covered_techniques / total_techniques * 100) if total_techniques else 0
        
        # Control type distribution
        control_type_distribution = defaultdict(int)
        for mapping in mappings:
            control_type_distribution[mapping.mapping_type] += 1
        
        # Generate recommendations
        recommendations = self._generate_coverage_recommendations(tactic_coverage, overall_coverage)
        
        coverage_analysis = {
            'framework': {
                'id': str(framework.id),
                'name': framework.name,
                'type': framework.framework_type
            },
            'analysis_date': datetime.utcnow().isoformat(),
            'overall_coverage': {
                'total_techniques': total_techniques,
                'covered_techniques': covered_techniques,
                'coverage_percentage': overall_coverage,
                'total_mappings': len(mappings)
            },
            'tactic_coverage': tactic_coverage,
            'control_type_distribution': dict(control_type_distribution),
            'recommendations': recommendations,
            'critical_gaps': self._identify_critical_gaps(tactic_coverage),
            'improvement_opportunities': self._identify_improvement_opportunities(mappings)
        }
        
        # Store coverage analysis
        await self._store_tactic_coverage_analysis(framework_id, tactic_coverage)
        
        return coverage_analysis
    
    def _generate_coverage_recommendations(
        self, 
        tactic_coverage: Dict[str, Any], 
        overall_coverage: float
    ) -> List[str]:
        """Generate recommendations based on coverage analysis"""
        
        recommendations = []
        
        if overall_coverage < 50:
            recommendations.append("Overall MITRE coverage is low. Consider implementing additional controls.")
        
        # Find tactics with low coverage
        low_coverage_tactics = [
            tactic for tactic, data in tactic_coverage.items() 
            if data['coverage_percentage'] < 30
        ]
        
        if low_coverage_tactics:
            recommendations.append(
                f"Focus on improving coverage for tactics: {', '.join(low_coverage_tactics[:3])}"
            )
        
        # Find tactics with no coverage
        no_coverage_tactics = [
            tactic for tactic, data in tactic_coverage.items() 
            if data['coverage_percentage'] == 0
        ]
        
        if no_coverage_tactics:
            recommendations.append(
                f"Implement controls for uncovered tactics: {', '.join(no_coverage_tactics[:3])}"
            )
        
        return recommendations
    
    def _identify_critical_gaps(self, tactic_coverage: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify critical gaps in MITRE coverage"""
        
        critical_gaps = []
        
        # High-priority tactics that should have good coverage
        critical_tactics = ['Initial Access', 'Execution', 'Persistence', 'Privilege Escalation', 'Defense Evasion']
        
        for tactic_name, data in tactic_coverage.items():
            if tactic_name in critical_tactics and data['coverage_percentage'] < 50:
                critical_gaps.append({
                    'tactic': tactic_name,
                    'coverage_percentage': data['coverage_percentage'],
                    'gap_count': len(data['gaps']),
                    'priority': 'critical' if data['coverage_percentage'] < 25 else 'high'
                })
        
        return critical_gaps
    
    def _identify_improvement_opportunities(
        self, 
        mappings: List[FrameworkMitreMapping]
    ) -> List[Dict[str, Any]]:
        """Identify opportunities for improving mappings"""
        
        opportunities = []
        
        # Find low-confidence mappings that could be improved
        low_confidence_mappings = [
            m for m in mappings 
            if m.confidence_level in ['low', 'medium'] and not m.is_validated
        ]
        
        if low_confidence_mappings:
            opportunities.append({
                'type': 'validation_needed',
                'description': f"{len(low_confidence_mappings)} mappings need validation",
                'count': len(low_confidence_mappings),
                'action': 'Review and validate low-confidence mappings'
            })
        
        # Find mappings without effectiveness scores
        unscored_mappings = [m for m in mappings if m.effectiveness_score is None]
        
        if unscored_mappings:
            opportunities.append({
                'type': 'effectiveness_scoring',
                'description': f"{len(unscored_mappings)} mappings lack effectiveness scores",
                'count': len(unscored_mappings),
                'action': 'Add effectiveness scores to improve analysis accuracy'
            })
        
        return opportunities
    
    async def _store_tactic_coverage_analysis(
        self, 
        framework_id: UUID, 
        tactic_coverage: Dict[str, Any]
    ):
        """Store tactic coverage analysis in the database"""
        
        for tactic_name, data in tactic_coverage.items():
            # Find tactic by name
            tactic = self.db.query(MitreTactic).filter(
                MitreTactic.name == tactic_name
            ).first()
            
            if not tactic:
                continue
            
            # Check if coverage record exists
            existing_coverage = self.db.query(FrameworkTacticCoverage).filter(
                and_(
                    FrameworkTacticCoverage.framework_id == framework_id,
                    FrameworkTacticCoverage.tactic_id == tactic.id,
                    FrameworkTacticCoverage.deleted_at.is_(None)
                )
            ).first()
            
            if existing_coverage:
                # Update existing record
                existing_coverage.total_techniques = data['total_techniques']
                existing_coverage.covered_techniques = data['covered_techniques']
                existing_coverage.coverage_percentage = data['coverage_percentage']
                existing_coverage.critical_gaps = data['gaps']
                existing_coverage.analysis_date = datetime.utcnow()
            else:
                # Create new record
                coverage_record = FrameworkTacticCoverage(
                    framework_id=framework_id,
                    tactic_id=tactic.id,
                    total_techniques=data['total_techniques'],
                    covered_techniques=data['covered_techniques'],
                    coverage_percentage=data['coverage_percentage'],
                    critical_gaps=data['gaps'],
                    analysis_date=datetime.utcnow()
                )
                self.db.add(coverage_record)
        
        self.db.commit()
    
    async def enrich_control_with_threat_intelligence(
        self,
        control_id: UUID,
        user_id: UUID = None
    ) -> ControlThreatIntelligence:
        """Enrich a control with threat intelligence from MITRE mappings"""
        
        # Get control and its MITRE mappings
        control = self.db.query(FrameworkControl).filter(
            and_(
                FrameworkControl.id == control_id,
                FrameworkControl.deleted_at.is_(None)
            )
        ).first()
        
        if not control:
            raise ValueError("Control not found")
        
        mappings = self.db.query(FrameworkMitreMapping).filter(
            and_(
                FrameworkMitreMapping.control_id == control_id,
                FrameworkMitreMapping.deleted_at.is_(None)
            )
        ).all()
        
        # Collect threat intelligence
        threat_actors = []
        attack_patterns = []
        threat_campaigns = []
        
        for mapping in mappings:
            technique = mapping.technique
            
            # Get threat actors using this technique
            # This would integrate with your MITRE threat actor data
            actors = self._get_technique_threat_actors(technique.id)
            threat_actors.extend(actors)
            
            # Get attack patterns
            patterns = self._get_technique_attack_patterns(technique.id)
            attack_patterns.extend(patterns)
        
        # Remove duplicates
        threat_actors = list({actor['id']: actor for actor in threat_actors}.values())
        attack_patterns = list({pattern['id']: pattern for pattern in attack_patterns}.values())
        
        # Calculate threat scores
        threat_likelihood = self._calculate_threat_likelihood(threat_actors, attack_patterns)
        threat_impact = self._calculate_threat_impact(control, mappings)
        overall_threat_score = (threat_likelihood + threat_impact) / 2
        
        # Check for existing threat intelligence
        existing_intel = self.db.query(ControlThreatIntelligence).filter(
            and_(
                ControlThreatIntelligence.control_id == control_id,
                ControlThreatIntelligence.deleted_at.is_(None)
            )
        ).first()
        
        if existing_intel:
            # Update existing record
            existing_intel.threat_actors = threat_actors
            existing_intel.attack_patterns = attack_patterns
            existing_intel.threat_campaigns = threat_campaigns
            existing_intel.overall_threat_score = overall_threat_score
            existing_intel.last_intelligence_update = datetime.utcnow()
            existing_intel.updated_by = user_id
            
            self.db.commit()
            return existing_intel
        
        # Create new threat intelligence record
        threat_intel = ControlThreatIntelligence(
            control_id=control_id,
            threat_actors=threat_actors,
            attack_patterns=attack_patterns,
            threat_campaigns=threat_campaigns,
            threat_likelihood='medium',  # This would be calculated based on data
            threat_impact='medium',      # This would be calculated based on data
            overall_threat_score=overall_threat_score,
            intelligence_sources=['mitre_attack'],
            confidence_score=0.8,  # High confidence for MITRE data
            created_by=user_id,
            updated_by=user_id
        )
        
        self.db.add(threat_intel)
        self.db.commit()
        self.db.refresh(threat_intel)
        
        return threat_intel
    
    def _get_technique_threat_actors(self, technique_id: UUID) -> List[Dict[str, Any]]:
        """Get threat actors that use a specific technique"""
        # This would query your MITRE threat actor data
        # Placeholder implementation
        return []
    
    def _get_technique_attack_patterns(self, technique_id: UUID) -> List[Dict[str, Any]]:
        """Get attack patterns for a specific technique"""
        # This would analyze attack patterns from threat intelligence
        # Placeholder implementation
        return []
    
    def _calculate_threat_likelihood(
        self, 
        threat_actors: List[Dict], 
        attack_patterns: List[Dict]
    ) -> float:
        """Calculate threat likelihood based on actor activity and patterns"""
        # Placeholder implementation
        return 0.5
    
    def _calculate_threat_impact(
        self, 
        control: FrameworkControl, 
        mappings: List[FrameworkMitreMapping]
    ) -> float:
        """Calculate threat impact based on control criticality and mappings"""
        # Placeholder implementation
        return 0.5
