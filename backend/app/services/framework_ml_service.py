"""
Framework Machine Learning Service
Provides advanced ML algorithms for cybersecurity framework analysis
"""

import asyncio
import logging
import numpy as np
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from uuid import UUID
from collections import defaultdict, Counter
from dataclasses import dataclass

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, text

from app.db.models.cybersecurity_frameworks import (
    CybersecurityFramework,
    FrameworkControl,
    ControlImplementation,
    ControlAssessment
)
from app.db.models.framework_mitre_mappings import (
    FrameworkMitreMapping,
    ControlThreatIntelligence
)

logger = logging.getLogger(__name__)


@dataclass
class ControlSimilarity:
    """Control similarity result"""
    control1_id: UUID
    control2_id: UUID
    similarity_score: float
    similarity_factors: Dict[str, float]
    confidence_level: str


@dataclass
class CompliancePrediction:
    """Compliance prediction result"""
    framework_id: UUID
    predicted_compliance: float
    confidence_interval: Tuple[float, float]
    prediction_date: datetime
    factors: Dict[str, float]


@dataclass
class RemediationRecommendation:
    """Remediation recommendation"""
    control_id: UUID
    recommendation_type: str
    priority_score: float
    estimated_effort: float
    estimated_cost: float
    success_probability: float
    rationale: str


class FrameworkMLService:
    """Machine Learning service for cybersecurity frameworks"""
    
    def __init__(self, db: Session):
        self.db = db
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during',
            'before', 'after', 'above', 'below', 'between', 'among', 'is', 'are',
            'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do',
            'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might',
            'must', 'can', 'this', 'that', 'these', 'those'
        }
        
        # Security domain keywords with weights
        self.security_keywords = {
            'access': 1.0, 'authentication': 1.0, 'authorization': 1.0,
            'encryption': 0.9, 'monitoring': 0.9, 'logging': 0.8,
            'backup': 0.8, 'recovery': 0.8, 'incident': 0.9,
            'vulnerability': 0.9, 'patch': 0.7, 'configuration': 0.7,
            'network': 0.8, 'firewall': 0.8, 'antivirus': 0.7,
            'malware': 0.8, 'audit': 0.7, 'compliance': 0.6,
            'risk': 0.8, 'security': 0.5, 'privacy': 0.7,
            'data': 0.6, 'system': 0.4, 'user': 0.5,
            'password': 0.8, 'credential': 0.8, 'certificate': 0.7,
            'key': 0.7, 'token': 0.7, 'session': 0.6,
            'endpoint': 0.7, 'server': 0.5, 'database': 0.6,
            'application': 0.5, 'service': 0.4, 'process': 0.4,
            'file': 0.5, 'directory': 0.5, 'registry': 0.6,
            'policy': 0.7, 'procedure': 0.6, 'control': 0.6,
            'detection': 0.8, 'prevention': 0.8, 'response': 0.8,
            'analysis': 0.6, 'forensics': 0.7
        }
    
    async def calculate_advanced_control_similarity(
        self,
        control1: FrameworkControl,
        control2: FrameworkControl
    ) -> ControlSimilarity:
        """Calculate advanced similarity between two controls using multiple ML techniques"""
        
        # Text-based similarity
        text_similarity = self._calculate_semantic_similarity(control1, control2)
        
        # Structural similarity
        structural_similarity = self._calculate_structural_similarity(control1, control2)
        
        # Context similarity (framework, domain, category)
        context_similarity = self._calculate_context_similarity(control1, control2)
        
        # Implementation pattern similarity
        implementation_similarity = await self._calculate_implementation_similarity(control1, control2)
        
        # MITRE mapping similarity
        mitre_similarity = await self._calculate_mitre_similarity(control1, control2)
        
        # Weighted combination
        similarity_factors = {
            'text_similarity': text_similarity,
            'structural_similarity': structural_similarity,
            'context_similarity': context_similarity,
            'implementation_similarity': implementation_similarity,
            'mitre_similarity': mitre_similarity
        }
        
        # Weights based on empirical analysis
        weights = {
            'text_similarity': 0.35,
            'structural_similarity': 0.20,
            'context_similarity': 0.15,
            'implementation_similarity': 0.15,
            'mitre_similarity': 0.15
        }
        
        overall_similarity = sum(
            similarity_factors[factor] * weights[factor]
            for factor in weights
        )
        
        # Determine confidence level
        confidence_level = self._determine_confidence_level(similarity_factors, overall_similarity)
        
        return ControlSimilarity(
            control1_id=control1.id,
            control2_id=control2.id,
            similarity_score=overall_similarity,
            similarity_factors=similarity_factors,
            confidence_level=confidence_level
        )
    
    def _calculate_semantic_similarity(
        self,
        control1: FrameworkControl,
        control2: FrameworkControl
    ) -> float:
        """Calculate semantic similarity using TF-IDF and cosine similarity"""
        
        # Combine text fields
        text1 = f"{control1.name} {control1.description or ''} {control1.objective or ''}"
        text2 = f"{control2.name} {control2.description or ''} {control2.objective or ''}"
        
        # Tokenize and clean
        tokens1 = self._tokenize_and_clean(text1)
        tokens2 = self._tokenize_and_clean(text2)
        
        if not tokens1 or not tokens2:
            return 0.0
        
        # Create vocabulary
        vocab = set(tokens1 + tokens2)
        
        # Calculate TF-IDF vectors
        tfidf1 = self._calculate_tfidf(tokens1, vocab)
        tfidf2 = self._calculate_tfidf(tokens2, vocab)
        
        # Calculate cosine similarity
        return self._cosine_similarity(tfidf1, tfidf2)
    
    def _tokenize_and_clean(self, text: str) -> List[str]:
        """Tokenize and clean text"""
        
        # Convert to lowercase and extract words
        words = re.findall(r'\b\w+\b', text.lower())
        
        # Remove stop words and short words
        cleaned_words = [
            word for word in words
            if word not in self.stop_words and len(word) > 2
        ]
        
        return cleaned_words
    
    def _calculate_tfidf(self, tokens: List[str], vocab: set) -> np.ndarray:
        """Calculate TF-IDF vector for tokens"""
        
        # Term frequency
        tf = Counter(tokens)
        total_terms = len(tokens)
        
        # Create TF-IDF vector
        tfidf_vector = np.zeros(len(vocab))
        
        for i, term in enumerate(sorted(vocab)):
            if term in tf:
                # TF: term frequency
                term_freq = tf[term] / total_terms
                
                # IDF: inverse document frequency (simplified)
                # In a full implementation, this would use a larger corpus
                idf = 1.0  # Simplified for this implementation
                
                # Apply security keyword weighting
                weight = self.security_keywords.get(term, 0.3)
                
                tfidf_vector[i] = term_freq * idf * weight
        
        return tfidf_vector
    
    def _cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """Calculate cosine similarity between two vectors"""
        
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    def _calculate_structural_similarity(
        self,
        control1: FrameworkControl,
        control2: FrameworkControl
    ) -> float:
        """Calculate structural similarity based on control properties"""
        
        similarity_score = 0.0
        
        # Control type similarity
        if control1.control_type and control2.control_type:
            if control1.control_type == control2.control_type:
                similarity_score += 0.3
            elif self._are_related_control_types(control1.control_type, control2.control_type):
                similarity_score += 0.15
        
        # Risk rating similarity
        if control1.risk_rating and control2.risk_rating:
            risk_similarity = self._calculate_risk_similarity(control1.risk_rating, control2.risk_rating)
            similarity_score += risk_similarity * 0.2
        
        # Automation level similarity
        if control1.automation_level and control2.automation_level:
            if control1.automation_level == control2.automation_level:
                similarity_score += 0.2
            elif self._are_related_automation_levels(control1.automation_level, control2.automation_level):
                similarity_score += 0.1
        
        # Priority similarity
        if control1.priority and control2.priority:
            if control1.priority == control2.priority:
                similarity_score += 0.15
        
        # Tags similarity
        if control1.tags and control2.tags:
            tag_similarity = self._calculate_tag_similarity(control1.tags, control2.tags)
            similarity_score += tag_similarity * 0.15
        
        return min(similarity_score, 1.0)
    
    def _calculate_context_similarity(
        self,
        control1: FrameworkControl,
        control2: FrameworkControl
    ) -> float:
        """Calculate context similarity based on framework, domain, category"""
        
        similarity_score = 0.0
        
        # Framework similarity
        if control1.framework_id == control2.framework_id:
            similarity_score += 0.4  # Same framework
        elif self._are_related_frameworks(control1.framework, control2.framework):
            similarity_score += 0.2  # Related frameworks
        
        # Domain similarity
        if control1.domain_id and control2.domain_id:
            if control1.domain_id == control2.domain_id:
                similarity_score += 0.3
            elif self._are_related_domains(control1.domain, control2.domain):
                similarity_score += 0.15
        
        # Category similarity
        if control1.category_id and control2.category_id:
            if control1.category_id == control2.category_id:
                similarity_score += 0.3
            elif self._are_related_categories(control1.category, control2.category):
                similarity_score += 0.15
        
        return min(similarity_score, 1.0)
    
    async def _calculate_implementation_similarity(
        self,
        control1: FrameworkControl,
        control2: FrameworkControl
    ) -> float:
        """Calculate similarity based on implementation patterns"""
        
        # Get implementations for both controls
        impl1 = self.db.query(ControlImplementation).filter(
            and_(
                ControlImplementation.control_id == control1.id,
                ControlImplementation.deleted_at.is_(None)
            )
        ).all()
        
        impl2 = self.db.query(ControlImplementation).filter(
            and_(
                ControlImplementation.control_id == control2.id,
                ControlImplementation.deleted_at.is_(None)
            )
        ).all()
        
        if not impl1 or not impl2:
            return 0.0
        
        # Compare implementation patterns
        similarity_factors = []
        
        for i1 in impl1:
            for i2 in impl2:
                # Status similarity
                status_sim = 1.0 if i1.status == i2.status else 0.0
                
                # Cost similarity (normalized)
                cost_sim = 0.0
                if i1.implementation_cost and i2.implementation_cost:
                    cost_ratio = min(i1.implementation_cost, i2.implementation_cost) / max(i1.implementation_cost, i2.implementation_cost)
                    cost_sim = cost_ratio
                
                # Effort similarity (normalized)
                effort_sim = 0.0
                if i1.effort_estimate and i2.effort_estimate:
                    effort_ratio = min(i1.effort_estimate, i2.effort_estimate) / max(i1.effort_estimate, i2.effort_estimate)
                    effort_sim = effort_ratio
                
                # Evidence similarity
                evidence_sim = self._calculate_evidence_similarity(i1.evidence or [], i2.evidence or [])
                
                impl_similarity = (status_sim * 0.4 + cost_sim * 0.2 + effort_sim * 0.2 + evidence_sim * 0.2)
                similarity_factors.append(impl_similarity)
        
        return max(similarity_factors) if similarity_factors else 0.0
    
    async def _calculate_mitre_similarity(
        self,
        control1: FrameworkControl,
        control2: FrameworkControl
    ) -> float:
        """Calculate similarity based on MITRE ATT&CK mappings"""
        
        # Get MITRE mappings for both controls
        mappings1 = self.db.query(FrameworkMitreMapping).filter(
            and_(
                FrameworkMitreMapping.control_id == control1.id,
                FrameworkMitreMapping.deleted_at.is_(None)
            )
        ).all()
        
        mappings2 = self.db.query(FrameworkMitreMapping).filter(
            and_(
                FrameworkMitreMapping.control_id == control2.id,
                FrameworkMitreMapping.deleted_at.is_(None)
            )
        ).all()
        
        if not mappings1 or not mappings2:
            return 0.0
        
        # Get technique IDs
        techniques1 = {mapping.technique_id for mapping in mappings1}
        techniques2 = {mapping.technique_id for mapping in mappings2}
        
        # Calculate Jaccard similarity
        intersection = len(techniques1.intersection(techniques2))
        union = len(techniques1.union(techniques2))
        
        jaccard_similarity = intersection / union if union > 0 else 0.0
        
        # Weight by mapping confidence
        confidence_weight = 0.0
        total_mappings = 0
        
        for mapping in mappings1 + mappings2:
            if mapping.confidence_level == 'high':
                confidence_weight += 1.0
            elif mapping.confidence_level == 'medium':
                confidence_weight += 0.7
            elif mapping.confidence_level == 'low':
                confidence_weight += 0.4
            total_mappings += 1
        
        avg_confidence = confidence_weight / total_mappings if total_mappings > 0 else 0.0
        
        return jaccard_similarity * avg_confidence
    
    def _are_related_control_types(self, type1: str, type2: str) -> bool:
        """Check if two control types are related"""
        related_types = {
            ('preventive', 'protective'),
            ('detective', 'monitoring'),
            ('corrective', 'responsive')
        }
        
        return (type1, type2) in related_types or (type2, type1) in related_types
    
    def _calculate_risk_similarity(self, risk1: str, risk2: str) -> float:
        """Calculate similarity between risk ratings"""
        risk_levels = {'low': 1, 'medium': 2, 'high': 3, 'critical': 4}
        
        level1 = risk_levels.get(risk1, 0)
        level2 = risk_levels.get(risk2, 0)
        
        if level1 == 0 or level2 == 0:
            return 0.0
        
        # Similarity decreases with distance
        distance = abs(level1 - level2)
        return max(0.0, 1.0 - (distance / 3.0))
    
    def _are_related_automation_levels(self, level1: str, level2: str) -> bool:
        """Check if two automation levels are related"""
        return abs(self._automation_level_score(level1) - self._automation_level_score(level2)) <= 1
    
    def _automation_level_score(self, level: str) -> int:
        """Convert automation level to numeric score"""
        scores = {'manual': 1, 'semi_automated': 2, 'automated': 3}
        return scores.get(level, 0)
    
    def _calculate_tag_similarity(self, tags1: List[str], tags2: List[str]) -> float:
        """Calculate similarity between tag lists"""
        if not tags1 or not tags2:
            return 0.0
        
        set1 = set(tags1)
        set2 = set(tags2)
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0
    
    def _are_related_frameworks(self, framework1, framework2) -> bool:
        """Check if two frameworks are related"""
        if not framework1 or not framework2:
            return False
        
        # Define framework relationships
        related_frameworks = {
            ('nist_csf_2_0', 'iso_27001_2022'),
            ('nist_csf_2_0', 'cis_controls_v8'),
            ('iso_27001_2022', 'isf_2022')
        }
        
        type1 = framework1.framework_type
        type2 = framework2.framework_type
        
        return (type1, type2) in related_frameworks or (type2, type1) in related_frameworks
    
    def _are_related_domains(self, domain1, domain2) -> bool:
        """Check if two domains are related"""
        if not domain1 or not domain2:
            return False
        
        # Simple keyword-based relationship check
        keywords1 = set(domain1.name.lower().split())
        keywords2 = set(domain2.name.lower().split())
        
        common_keywords = keywords1.intersection(keywords2)
        return len(common_keywords) > 0
    
    def _are_related_categories(self, category1, category2) -> bool:
        """Check if two categories are related"""
        if not category1 or not category2:
            return False
        
        # Simple keyword-based relationship check
        keywords1 = set(category1.name.lower().split())
        keywords2 = set(category2.name.lower().split())
        
        common_keywords = keywords1.intersection(keywords2)
        return len(common_keywords) > 0
    
    def _calculate_evidence_similarity(self, evidence1: List[Dict], evidence2: List[Dict]) -> float:
        """Calculate similarity between evidence lists"""
        if not evidence1 or not evidence2:
            return 0.0
        
        # Extract evidence types
        types1 = {item.get('type', '') for item in evidence1}
        types2 = {item.get('type', '') for item in evidence2}
        
        # Calculate Jaccard similarity
        intersection = len(types1.intersection(types2))
        union = len(types1.union(types2))
        
        return intersection / union if union > 0 else 0.0
    
    def _determine_confidence_level(
        self,
        similarity_factors: Dict[str, float],
        overall_similarity: float
    ) -> str:
        """Determine confidence level for similarity calculation"""
        
        # High confidence: multiple factors agree and overall score is high
        high_factors = sum(1 for score in similarity_factors.values() if score > 0.7)
        
        if overall_similarity > 0.8 and high_factors >= 3:
            return 'high'
        elif overall_similarity > 0.6 and high_factors >= 2:
            return 'medium'
        else:
            return 'low'

    async def predict_compliance_trajectory(
        self,
        framework_id: UUID,
        prediction_horizon_days: int = 90
    ) -> CompliancePrediction:
        """Predict compliance trajectory using historical data and ML"""

        # Get historical compliance data
        historical_data = await self._get_historical_compliance_data(framework_id, days=180)

        if len(historical_data) < 5:
            # Not enough data for prediction
            current_compliance = await self._get_current_compliance(framework_id)
            return CompliancePrediction(
                framework_id=framework_id,
                predicted_compliance=current_compliance,
                confidence_interval=(current_compliance - 5, current_compliance + 5),
                prediction_date=datetime.utcnow() + timedelta(days=prediction_horizon_days),
                factors={'insufficient_data': 1.0}
            )

        # Extract features for prediction
        features = self._extract_prediction_features(historical_data, framework_id)

        # Simple linear regression prediction (in production, use more sophisticated models)
        predicted_compliance = self._predict_using_linear_regression(features, prediction_horizon_days)

        # Calculate confidence interval
        confidence_interval = self._calculate_confidence_interval(predicted_compliance, features)

        # Identify key factors
        factors = self._identify_prediction_factors(features)

        return CompliancePrediction(
            framework_id=framework_id,
            predicted_compliance=predicted_compliance,
            confidence_interval=confidence_interval,
            prediction_date=datetime.utcnow() + timedelta(days=prediction_horizon_days),
            factors=factors
        )

    async def generate_intelligent_remediation_recommendations(
        self,
        framework_id: UUID,
        max_recommendations: int = 10
    ) -> List[RemediationRecommendation]:
        """Generate intelligent remediation recommendations using ML"""

        # Get all controls with gaps
        controls_with_gaps = await self._get_controls_with_gaps(framework_id)

        recommendations = []

        for control in controls_with_gaps:
            # Calculate priority score
            priority_score = await self._calculate_remediation_priority(control)

            # Estimate effort and cost
            effort_estimate = await self._estimate_remediation_effort(control)
            cost_estimate = await self._estimate_remediation_cost(control)

            # Predict success probability
            success_probability = await self._predict_remediation_success(control)

            # Generate rationale
            rationale = await self._generate_remediation_rationale(control, priority_score)

            # Determine recommendation type
            recommendation_type = self._determine_recommendation_type(control)

            recommendation = RemediationRecommendation(
                control_id=control.id,
                recommendation_type=recommendation_type,
                priority_score=priority_score,
                estimated_effort=effort_estimate,
                estimated_cost=cost_estimate,
                success_probability=success_probability,
                rationale=rationale
            )

            recommendations.append(recommendation)

        # Sort by priority score and return top recommendations
        recommendations.sort(key=lambda x: x.priority_score, reverse=True)
        return recommendations[:max_recommendations]

    async def _get_historical_compliance_data(self, framework_id: UUID, days: int) -> List[Dict[str, Any]]:
        """Get historical compliance data for a framework"""

        # In a production system, this would query stored compliance snapshots
        # For now, we'll simulate historical data based on current state

        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)

        # Get current compliance
        current_compliance = await self._get_current_compliance(framework_id)

        # Simulate historical data points
        historical_data = []
        for i in range(0, days, 7):  # Weekly data points
            date = start_date + timedelta(days=i)

            # Simulate compliance progression (simplified)
            progress_factor = i / days
            simulated_compliance = max(0, current_compliance - (1 - progress_factor) * 20)

            historical_data.append({
                'date': date,
                'compliance_percentage': simulated_compliance,
                'implemented_controls': int(simulated_compliance * 0.5),  # Simplified
                'assessment_count': max(1, int(progress_factor * 10))
            })

        return historical_data

    async def _get_current_compliance(self, framework_id: UUID) -> float:
        """Get current compliance percentage for a framework"""

        total_controls = self.db.query(FrameworkControl).filter(
            and_(
                FrameworkControl.framework_id == framework_id,
                FrameworkControl.deleted_at.is_(None)
            )
        ).count()

        if total_controls == 0:
            return 0.0

        implemented_controls = self.db.query(ControlImplementation).join(FrameworkControl).filter(
            and_(
                FrameworkControl.framework_id == framework_id,
                ControlImplementation.status.in_(['implemented', 'verified']),
                ControlImplementation.deleted_at.is_(None),
                FrameworkControl.deleted_at.is_(None)
            )
        ).count()

        return (implemented_controls / total_controls) * 100

    def _extract_prediction_features(self, historical_data: List[Dict], framework_id: UUID) -> Dict[str, Any]:
        """Extract features for compliance prediction"""

        if not historical_data:
            return {}

        # Time series features
        compliance_values = [point['compliance_percentage'] for point in historical_data]

        features = {
            'current_compliance': compliance_values[-1] if compliance_values else 0,
            'compliance_trend': self._calculate_trend(compliance_values),
            'compliance_volatility': np.std(compliance_values) if len(compliance_values) > 1 else 0,
            'assessment_frequency': np.mean([point['assessment_count'] for point in historical_data]),
            'implementation_velocity': self._calculate_implementation_velocity(historical_data),
            'seasonal_factor': self._calculate_seasonal_factor(historical_data)
        }

        return features

    def _calculate_trend(self, values: List[float]) -> float:
        """Calculate trend in compliance values"""
        if len(values) < 2:
            return 0.0

        # Simple linear trend calculation
        x = np.arange(len(values))
        y = np.array(values)

        # Calculate slope
        slope = np.polyfit(x, y, 1)[0]
        return slope

    def _calculate_implementation_velocity(self, historical_data: List[Dict]) -> float:
        """Calculate implementation velocity"""
        if len(historical_data) < 2:
            return 0.0

        # Calculate average change in implemented controls per week
        velocities = []
        for i in range(1, len(historical_data)):
            prev_impl = historical_data[i-1]['implemented_controls']
            curr_impl = historical_data[i]['implemented_controls']
            velocity = curr_impl - prev_impl
            velocities.append(velocity)

        return np.mean(velocities) if velocities else 0.0

    def _calculate_seasonal_factor(self, historical_data: List[Dict]) -> float:
        """Calculate seasonal factor (simplified)"""
        # In a real implementation, this would analyze seasonal patterns
        # For now, return a neutral factor
        return 1.0

    def _predict_using_linear_regression(self, features: Dict[str, Any], horizon_days: int) -> float:
        """Predict compliance using linear regression (simplified)"""

        current_compliance = features.get('current_compliance', 0)
        trend = features.get('compliance_trend', 0)
        velocity = features.get('implementation_velocity', 0)

        # Simple prediction: current + trend * time + velocity factor
        weeks_ahead = horizon_days / 7
        predicted_change = trend * weeks_ahead + velocity * weeks_ahead * 0.5

        predicted_compliance = current_compliance + predicted_change

        # Bound between 0 and 100
        return max(0, min(100, predicted_compliance))

    def _calculate_confidence_interval(self, prediction: float, features: Dict[str, Any]) -> Tuple[float, float]:
        """Calculate confidence interval for prediction"""

        volatility = features.get('compliance_volatility', 5)

        # Simple confidence interval based on historical volatility
        margin = volatility * 1.96  # 95% confidence interval

        lower_bound = max(0, prediction - margin)
        upper_bound = min(100, prediction + margin)

        return (lower_bound, upper_bound)

    def _identify_prediction_factors(self, features: Dict[str, Any]) -> Dict[str, float]:
        """Identify key factors affecting the prediction"""

        factors = {}

        trend = features.get('compliance_trend', 0)
        if trend > 1:
            factors['positive_trend'] = min(trend / 5, 1.0)
        elif trend < -1:
            factors['negative_trend'] = min(abs(trend) / 5, 1.0)

        velocity = features.get('implementation_velocity', 0)
        if velocity > 0:
            factors['implementation_momentum'] = min(velocity / 3, 1.0)

        volatility = features.get('compliance_volatility', 0)
        if volatility > 10:
            factors['high_volatility'] = min(volatility / 20, 1.0)

        assessment_freq = features.get('assessment_frequency', 0)
        if assessment_freq > 2:
            factors['active_assessment'] = min(assessment_freq / 5, 1.0)

        return factors

    async def _get_controls_with_gaps(self, framework_id: UUID) -> List[FrameworkControl]:
        """Get controls that have implementation gaps"""

        controls = self.db.query(FrameworkControl).outerjoin(ControlImplementation).filter(
            and_(
                FrameworkControl.framework_id == framework_id,
                or_(
                    ControlImplementation.status.in_(['not_implemented', 'partially_implemented']),
                    ControlImplementation.id.is_(None)  # No implementation record
                ),
                FrameworkControl.deleted_at.is_(None)
            )
        ).all()

        return controls

    async def _calculate_remediation_priority(self, control: FrameworkControl) -> float:
        """Calculate priority score for remediation"""

        priority_score = 0.0

        # Risk rating factor
        risk_weights = {'critical': 1.0, 'high': 0.8, 'medium': 0.5, 'low': 0.2}
        priority_score += risk_weights.get(control.risk_rating, 0.3) * 0.4

        # MITRE coverage factor
        mitre_mappings = self.db.query(FrameworkMitreMapping).filter(
            and_(
                FrameworkMitreMapping.control_id == control.id,
                FrameworkMitreMapping.deleted_at.is_(None)
            )
        ).count()

        mitre_factor = min(mitre_mappings / 5, 1.0)  # Normalize to 0-1
        priority_score += mitre_factor * 0.3

        # Implementation complexity factor (inverse)
        complexity_factor = 0.5  # Default medium complexity
        if control.metadata and 'complexity' in control.metadata:
            complexity_weights = {'simple': 1.0, 'moderate': 0.7, 'complex': 0.4}
            complexity_factor = complexity_weights.get(control.metadata['complexity'], 0.5)

        priority_score += complexity_factor * 0.2

        # Dependency factor
        dependency_factor = await self._calculate_dependency_factor(control)
        priority_score += dependency_factor * 0.1

        return min(priority_score, 1.0)

    async def _estimate_remediation_effort(self, control: FrameworkControl) -> float:
        """Estimate effort required for remediation"""

        # Get similar controls and their effort estimates
        similar_controls = await self._find_similar_controls(control, limit=5)

        effort_estimates = []
        for similar_control in similar_controls:
            implementations = self.db.query(ControlImplementation).filter(
                and_(
                    ControlImplementation.control_id == similar_control.id,
                    ControlImplementation.effort_estimate.isnot(None),
                    ControlImplementation.deleted_at.is_(None)
                )
            ).all()

            for impl in implementations:
                effort_estimates.append(impl.effort_estimate)

        if effort_estimates:
            return np.median(effort_estimates)

        # Default estimates based on control characteristics
        base_effort = 40.0  # Base 40 hours

        # Adjust based on risk rating
        risk_multipliers = {'critical': 1.5, 'high': 1.3, 'medium': 1.0, 'low': 0.7}
        multiplier = risk_multipliers.get(control.risk_rating, 1.0)

        # Adjust based on automation level
        automation_multipliers = {'manual': 1.2, 'semi_automated': 1.0, 'automated': 0.8}
        automation_multiplier = automation_multipliers.get(control.automation_level, 1.0)

        return base_effort * multiplier * automation_multiplier

    async def _estimate_remediation_cost(self, control: FrameworkControl) -> float:
        """Estimate cost for remediation"""

        effort_hours = await self._estimate_remediation_effort(control)

        # Average hourly rate for security implementation
        hourly_rate = 150.0  # $150/hour

        base_cost = effort_hours * hourly_rate

        # Add tool/technology costs
        technology_cost = 0.0
        if control.automation_level == 'automated':
            technology_cost = 5000.0  # Base technology cost
        elif control.automation_level == 'semi_automated':
            technology_cost = 2000.0

        return base_cost + technology_cost

    async def _predict_remediation_success(self, control: FrameworkControl) -> float:
        """Predict probability of successful remediation"""

        success_probability = 0.7  # Base probability

        # Adjust based on control characteristics
        if control.automation_level == 'automated':
            success_probability += 0.2
        elif control.automation_level == 'manual':
            success_probability -= 0.1

        # Adjust based on risk rating (higher risk = more attention = higher success)
        risk_adjustments = {'critical': 0.15, 'high': 0.1, 'medium': 0.0, 'low': -0.05}
        success_probability += risk_adjustments.get(control.risk_rating, 0.0)

        # Adjust based on implementation guidance availability
        if control.implementation_guidance:
            success_probability += 0.1

        return min(max(success_probability, 0.1), 0.95)

    async def _generate_remediation_rationale(self, control: FrameworkControl, priority_score: float) -> str:
        """Generate rationale for remediation recommendation"""

        rationale_parts = []

        if control.risk_rating in ['critical', 'high']:
            rationale_parts.append(f"High-priority due to {control.risk_rating} risk rating")

        mitre_count = self.db.query(FrameworkMitreMapping).filter(
            and_(
                FrameworkMitreMapping.control_id == control.id,
                FrameworkMitreMapping.deleted_at.is_(None)
            )
        ).count()

        if mitre_count > 0:
            rationale_parts.append(f"Addresses {mitre_count} MITRE ATT&CK techniques")

        if priority_score > 0.8:
            rationale_parts.append("Critical for overall security posture")
        elif priority_score > 0.6:
            rationale_parts.append("Important for compliance and security")

        if control.automation_level == 'automated':
            rationale_parts.append("Can be automated for ongoing effectiveness")

        return "; ".join(rationale_parts) if rationale_parts else "Standard remediation priority"

    def _determine_recommendation_type(self, control: FrameworkControl) -> str:
        """Determine the type of remediation recommendation"""

        if control.automation_level == 'automated':
            return "automation_implementation"
        elif control.risk_rating in ['critical', 'high']:
            return "priority_implementation"
        elif control.control_type == 'detective':
            return "monitoring_enhancement"
        elif control.control_type == 'preventive':
            return "preventive_control"
        else:
            return "standard_implementation"

    async def _calculate_dependency_factor(self, control: FrameworkControl) -> float:
        """Calculate dependency factor for a control"""
        # Simplified implementation - in production, this would analyze control dependencies
        return 0.5

    async def _find_similar_controls(self, control: FrameworkControl, limit: int = 5) -> List[FrameworkControl]:
        """Find similar controls for effort estimation"""

        # Get controls with similar characteristics
        similar_controls = self.db.query(FrameworkControl).filter(
            and_(
                FrameworkControl.id != control.id,
                FrameworkControl.control_type == control.control_type,
                FrameworkControl.risk_rating == control.risk_rating,
                FrameworkControl.deleted_at.is_(None)
            )
        ).limit(limit).all()

        return similar_controls
