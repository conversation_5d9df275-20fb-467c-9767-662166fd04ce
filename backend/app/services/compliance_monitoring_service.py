"""
Real-time Compliance Monitoring Service
Provides real-time monitoring and alerting for cybersecurity framework compliance
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from uuid import UUID
from collections import defaultdict

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, text

from app.db.models.cybersecurity_frameworks import (
    CybersecurityFramework,
    FrameworkControl,
    ControlImplementation,
    ControlAssessment
)
from app.db.models.framework_mitre_mappings import (
    FrameworkComplianceMetrics,
    ControlThreatIntelligence
)
from app.api.v1.realtime import websocket_manager

logger = logging.getLogger(__name__)


class ComplianceMonitoringService:
    """Real-time compliance monitoring and alerting service"""
    
    def __init__(self, db: Session):
        self.db = db
        self.monitoring_active = False
        self.alert_thresholds = {
            'critical_compliance': 50.0,  # Alert if compliance drops below 50%
            'high_risk_gaps': 5,          # Alert if more than 5 high-risk gaps
            'overdue_assessments': 10,    # Alert if more than 10 assessments overdue
            'implementation_delays': 7    # Alert if implementations delayed by 7+ days
        }
    
    async def start_real_time_monitoring(self, user_id: UUID):
        """Start real-time compliance monitoring for a user"""
        
        self.monitoring_active = True
        logger.info(f"Starting real-time compliance monitoring for user {user_id}")
        
        while self.monitoring_active:
            try:
                # Get current compliance data
                compliance_data = await self.get_real_time_compliance_data(user_id)
                
                # Check for alerts
                alerts = await self.check_compliance_alerts(compliance_data)
                
                # Prepare monitoring update
                monitoring_update = {
                    "type": "compliance_monitoring",
                    "data": compliance_data,
                    "alerts": alerts,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                # Send update via WebSocket
                await websocket_manager.send_personal_message(
                    user_id,
                    monitoring_update
                )
                
                # Wait before next update
                await asyncio.sleep(30)  # Update every 30 seconds
                
            except Exception as e:
                logger.error(f"Compliance monitoring error: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    def stop_monitoring(self):
        """Stop real-time monitoring"""
        self.monitoring_active = False
        logger.info("Stopped real-time compliance monitoring")
    
    async def get_real_time_compliance_data(self, user_id: UUID = None) -> Dict[str, Any]:
        """Get current compliance data for real-time dashboard"""
        
        # Get framework compliance summary
        frameworks_summary = await self.get_frameworks_compliance_summary()
        
        # Get recent assessment activities
        recent_assessments = await self.get_recent_assessments(limit=10)
        
        # Get compliance trends
        trends = await self.get_compliance_trends(days=30)
        
        # Get critical gaps
        critical_gaps = await self.get_critical_compliance_gaps()
        
        # Get implementation status
        implementation_status = await self.get_implementation_status()
        
        # Get overdue items
        overdue_items = await self.get_overdue_items()
        
        return {
            "frameworks_summary": frameworks_summary,
            "recent_assessments": recent_assessments,
            "trends": trends,
            "critical_gaps": critical_gaps,
            "implementation_status": implementation_status,
            "overdue_items": overdue_items,
            "last_updated": datetime.utcnow().isoformat()
        }
    
    async def get_frameworks_compliance_summary(self) -> List[Dict[str, Any]]:
        """Get compliance summary for all frameworks"""
        
        frameworks = self.db.query(CybersecurityFramework).filter(
            CybersecurityFramework.deleted_at.is_(None)
        ).all()
        
        summary = []
        
        for framework in frameworks:
            # Get controls count
            total_controls = self.db.query(FrameworkControl).filter(
                and_(
                    FrameworkControl.framework_id == framework.id,
                    FrameworkControl.deleted_at.is_(None)
                )
            ).count()
            
            # Get implementation statistics
            implementations = self.db.query(ControlImplementation).join(FrameworkControl).filter(
                and_(
                    FrameworkControl.framework_id == framework.id,
                    ControlImplementation.deleted_at.is_(None),
                    FrameworkControl.deleted_at.is_(None)
                )
            ).all()
            
            # Count by status
            status_counts = defaultdict(int)
            for impl in implementations:
                status_counts[impl.status] += 1
            
            # Calculate compliance percentage
            implemented_count = status_counts.get('implemented', 0) + status_counts.get('verified', 0)
            compliance_percentage = (implemented_count / total_controls * 100) if total_controls > 0 else 0
            
            # Get recent trend
            trend = await self.calculate_compliance_trend(framework.id, days=7)
            
            summary.append({
                "framework_id": str(framework.id),
                "framework_name": framework.name,
                "framework_type": framework.framework_type,
                "total_controls": total_controls,
                "implemented": status_counts.get('implemented', 0),
                "partially_implemented": status_counts.get('partially_implemented', 0),
                "not_implemented": status_counts.get('not_implemented', 0),
                "compliance_percentage": round(compliance_percentage, 1),
                "trend": trend,
                "status": self.get_compliance_status(compliance_percentage)
            })
        
        return summary
    
    async def get_recent_assessments(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent assessment activities"""
        
        assessments = self.db.query(ControlAssessment).join(FrameworkControl).filter(
            ControlAssessment.deleted_at.is_(None)
        ).order_by(ControlAssessment.assessment_date.desc()).limit(limit).all()
        
        return [
            {
                "assessment_id": str(assessment.id),
                "control_id": assessment.control.control_id if assessment.control else "Unknown",
                "control_name": assessment.control.name if assessment.control else "Unknown",
                "framework_name": assessment.control.framework.name if assessment.control and assessment.control.framework else "Unknown",
                "assessor": assessment.assessor,
                "assessment_date": assessment.assessment_date.isoformat(),
                "status": assessment.status,
                "compliance_score": assessment.compliance_score,
                "effectiveness_rating": assessment.effectiveness_rating
            }
            for assessment in assessments
        ]
    
    async def get_compliance_trends(self, days: int = 30) -> Dict[str, Any]:
        """Get compliance trends over specified period"""
        
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Get daily compliance snapshots (simplified - in production you'd store daily snapshots)
        frameworks = self.db.query(CybersecurityFramework).filter(
            CybersecurityFramework.deleted_at.is_(None)
        ).all()
        
        trends = {
            "period_days": days,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "framework_trends": [],
            "overall_trend": {
                "direction": "stable",
                "change_percentage": 0.0
            }
        }
        
        for framework in frameworks:
            # Calculate trend for this framework
            trend = await self.calculate_compliance_trend(framework.id, days)
            
            trends["framework_trends"].append({
                "framework_id": str(framework.id),
                "framework_name": framework.name,
                "trend_direction": trend["direction"],
                "change_percentage": trend["change_percentage"],
                "current_compliance": trend["current_compliance"]
            })
        
        return trends
    
    async def calculate_compliance_trend(self, framework_id: UUID, days: int) -> Dict[str, Any]:
        """Calculate compliance trend for a framework"""
        
        # Get current compliance
        current_compliance = await self.get_framework_compliance_percentage(framework_id)
        
        # Get compliance from 'days' ago (simplified calculation)
        # In production, you'd store daily compliance snapshots
        past_date = datetime.utcnow() - timedelta(days=days)
        
        # For now, simulate trend calculation
        # This would be replaced with actual historical data
        change_percentage = 0.0  # Placeholder
        
        if change_percentage > 1.0:
            direction = "improving"
        elif change_percentage < -1.0:
            direction = "declining"
        else:
            direction = "stable"
        
        return {
            "direction": direction,
            "change_percentage": change_percentage,
            "current_compliance": current_compliance
        }
    
    async def get_framework_compliance_percentage(self, framework_id: UUID) -> float:
        """Get current compliance percentage for a framework"""
        
        total_controls = self.db.query(FrameworkControl).filter(
            and_(
                FrameworkControl.framework_id == framework_id,
                FrameworkControl.deleted_at.is_(None)
            )
        ).count()
        
        if total_controls == 0:
            return 0.0
        
        implemented_controls = self.db.query(ControlImplementation).join(FrameworkControl).filter(
            and_(
                FrameworkControl.framework_id == framework_id,
                ControlImplementation.status.in_(['implemented', 'verified']),
                ControlImplementation.deleted_at.is_(None),
                FrameworkControl.deleted_at.is_(None)
            )
        ).count()
        
        return (implemented_controls / total_controls) * 100
    
    async def get_critical_compliance_gaps(self) -> List[Dict[str, Any]]:
        """Get critical compliance gaps requiring immediate attention"""
        
        # Get controls with high/critical risk that are not implemented
        critical_gaps = self.db.query(FrameworkControl).outerjoin(ControlImplementation).filter(
            and_(
                FrameworkControl.risk_rating.in_(['high', 'critical']),
                or_(
                    ControlImplementation.status.in_(['not_implemented', 'partially_implemented']),
                    ControlImplementation.id.is_(None)  # No implementation record
                ),
                FrameworkControl.deleted_at.is_(None)
            )
        ).limit(20).all()
        
        gaps = []
        for control in critical_gaps:
            # Get latest implementation if exists
            latest_impl = self.db.query(ControlImplementation).filter(
                and_(
                    ControlImplementation.control_id == control.id,
                    ControlImplementation.deleted_at.is_(None)
                )
            ).order_by(ControlImplementation.updated_at.desc()).first()
            
            gaps.append({
                "control_id": control.control_id,
                "control_name": control.name,
                "framework_name": control.framework.name if control.framework else "Unknown",
                "risk_rating": control.risk_rating,
                "current_status": latest_impl.status if latest_impl else "not_implemented",
                "target_date": latest_impl.target_completion_date.isoformat() if latest_impl and latest_impl.target_completion_date else None,
                "responsible_party": latest_impl.responsible_party if latest_impl else None,
                "days_overdue": self.calculate_days_overdue(latest_impl) if latest_impl else None
            })
        
        return gaps
    
    async def get_implementation_status(self) -> Dict[str, Any]:
        """Get overall implementation status across all frameworks"""
        
        # Get all implementations
        implementations = self.db.query(ControlImplementation).filter(
            ControlImplementation.deleted_at.is_(None)
        ).all()
        
        status_counts = defaultdict(int)
        total_cost = 0.0
        total_effort = 0.0
        
        for impl in implementations:
            status_counts[impl.status] += 1
            if impl.implementation_cost:
                total_cost += impl.implementation_cost
            if impl.effort_estimate:
                total_effort += impl.effort_estimate
        
        return {
            "total_implementations": len(implementations),
            "status_distribution": dict(status_counts),
            "total_estimated_cost": total_cost,
            "total_estimated_effort_hours": total_effort,
            "average_cost_per_control": total_cost / len(implementations) if implementations else 0,
            "average_effort_per_control": total_effort / len(implementations) if implementations else 0
        }
    
    async def get_overdue_items(self) -> Dict[str, Any]:
        """Get overdue assessments and implementations"""
        
        current_date = datetime.utcnow()
        
        # Overdue implementations
        overdue_implementations = self.db.query(ControlImplementation).filter(
            and_(
                ControlImplementation.target_completion_date < current_date,
                ControlImplementation.status.in_(['not_implemented', 'partially_implemented']),
                ControlImplementation.deleted_at.is_(None)
            )
        ).all()
        
        # Overdue assessments
        overdue_assessments = self.db.query(ControlAssessment).filter(
            and_(
                ControlAssessment.next_assessment_date < current_date,
                ControlAssessment.deleted_at.is_(None)
            )
        ).all()
        
        return {
            "overdue_implementations": [
                {
                    "control_id": impl.control.control_id if impl.control else "Unknown",
                    "control_name": impl.control.name if impl.control else "Unknown",
                    "target_date": impl.target_completion_date.isoformat(),
                    "days_overdue": (current_date - impl.target_completion_date).days,
                    "responsible_party": impl.responsible_party,
                    "status": impl.status
                }
                for impl in overdue_implementations
            ],
            "overdue_assessments": [
                {
                    "control_id": assess.control.control_id if assess.control else "Unknown",
                    "control_name": assess.control.name if assess.control else "Unknown",
                    "due_date": assess.next_assessment_date.isoformat(),
                    "days_overdue": (current_date - assess.next_assessment_date).days,
                    "last_assessment": assess.assessment_date.isoformat(),
                    "frequency": assess.assessment_frequency
                }
                for assess in overdue_assessments
            ]
        }
    
    async def check_compliance_alerts(self, compliance_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check for compliance alerts based on thresholds"""
        
        alerts = []
        
        # Check critical compliance threshold
        for framework in compliance_data["frameworks_summary"]:
            if framework["compliance_percentage"] < self.alert_thresholds["critical_compliance"]:
                alerts.append({
                    "type": "critical_compliance",
                    "severity": "critical",
                    "title": f"Critical Compliance Alert: {framework['framework_name']}",
                    "message": f"Compliance dropped to {framework['compliance_percentage']:.1f}% (threshold: {self.alert_thresholds['critical_compliance']}%)",
                    "framework_id": framework["framework_id"],
                    "framework_name": framework["framework_name"],
                    "current_value": framework["compliance_percentage"],
                    "threshold": self.alert_thresholds["critical_compliance"],
                    "timestamp": datetime.utcnow().isoformat()
                })
        
        # Check high-risk gaps
        critical_gaps_count = len(compliance_data["critical_gaps"])
        if critical_gaps_count > self.alert_thresholds["high_risk_gaps"]:
            alerts.append({
                "type": "high_risk_gaps",
                "severity": "high",
                "title": "High-Risk Compliance Gaps",
                "message": f"{critical_gaps_count} high-risk gaps identified (threshold: {self.alert_thresholds['high_risk_gaps']})",
                "current_value": critical_gaps_count,
                "threshold": self.alert_thresholds["high_risk_gaps"],
                "timestamp": datetime.utcnow().isoformat()
            })
        
        # Check overdue assessments
        overdue_assessments_count = len(compliance_data["overdue_items"]["overdue_assessments"])
        if overdue_assessments_count > self.alert_thresholds["overdue_assessments"]:
            alerts.append({
                "type": "overdue_assessments",
                "severity": "medium",
                "title": "Overdue Assessments",
                "message": f"{overdue_assessments_count} assessments are overdue (threshold: {self.alert_thresholds['overdue_assessments']})",
                "current_value": overdue_assessments_count,
                "threshold": self.alert_thresholds["overdue_assessments"],
                "timestamp": datetime.utcnow().isoformat()
            })
        
        # Check implementation delays
        overdue_implementations = compliance_data["overdue_items"]["overdue_implementations"]
        severely_overdue = [impl for impl in overdue_implementations if impl["days_overdue"] >= self.alert_thresholds["implementation_delays"]]
        
        if len(severely_overdue) > 0:
            alerts.append({
                "type": "implementation_delays",
                "severity": "high",
                "title": "Severely Overdue Implementations",
                "message": f"{len(severely_overdue)} implementations are {self.alert_thresholds['implementation_delays']}+ days overdue",
                "current_value": len(severely_overdue),
                "threshold": self.alert_thresholds["implementation_delays"],
                "timestamp": datetime.utcnow().isoformat()
            })
        
        return alerts
    
    def get_compliance_status(self, compliance_percentage: float) -> str:
        """Get compliance status based on percentage"""
        if compliance_percentage >= 90:
            return "excellent"
        elif compliance_percentage >= 75:
            return "good"
        elif compliance_percentage >= 50:
            return "fair"
        else:
            return "poor"
    
    def calculate_days_overdue(self, implementation: ControlImplementation) -> int:
        """Calculate days overdue for an implementation"""
        if not implementation.target_completion_date:
            return 0
        
        current_date = datetime.utcnow()
        if current_date > implementation.target_completion_date:
            return (current_date - implementation.target_completion_date).days
        
        return 0
    
    async def send_compliance_alert(self, alert: Dict[str, Any], user_ids: List[UUID]):
        """Send compliance alert to specified users"""
        
        alert_message = {
            "type": "compliance_alert",
            "alert": alert,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        for user_id in user_ids:
            try:
                await websocket_manager.send_personal_message(user_id, alert_message)
            except Exception as e:
                logger.error(f"Failed to send alert to user {user_id}: {e}")
    
    async def update_alert_thresholds(self, new_thresholds: Dict[str, Any]):
        """Update alert thresholds"""
        self.alert_thresholds.update(new_thresholds)
        logger.info(f"Updated alert thresholds: {self.alert_thresholds}")
