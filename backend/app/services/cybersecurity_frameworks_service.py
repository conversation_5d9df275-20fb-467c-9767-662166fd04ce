"""
Cybersecurity Frameworks Service
Provides business logic and data processing for cybersecurity frameworks
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from uuid import UUID
import re

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, text

from app.db.models.cybersecurity_frameworks import (
    CybersecurityFramework,
    FrameworkDomain,
    FrameworkCategory,
    FrameworkControl,
    ControlImplementation,
    ControlAssessment,
    FrameworkType,
    ControlStatus,
    RiskLevel
)

logger = logging.getLogger(__name__)


class CybersecurityFrameworksService:
    """Service for managing cybersecurity frameworks and compliance"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def import_framework_data(
        self,
        framework_type: FrameworkType,
        framework_data: Dict[str, Any],
        user_id: UUID
    ) -> CybersecurityFramework:
        """Import framework data from external sources"""
        
        try:
            # Create framework
            framework = CybersecurityFramework(
                name=framework_data["name"],
                framework_type=framework_type.value,
                version=framework_data["version"],
                description=framework_data.get("description"),
                authority=framework_data.get("authority"),
                publication_date=framework_data.get("publication_date"),
                effective_date=framework_data.get("effective_date"),
                metadata=framework_data.get("metadata", {}),
                created_by=user_id,
                updated_by=user_id
            )
            
            self.db.add(framework)
            self.db.flush()  # Get the ID
            
            # Import domains
            for domain_data in framework_data.get("domains", []):
                domain = FrameworkDomain(
                    framework_id=framework.id,
                    domain_id=domain_data["domain_id"],
                    name=domain_data["name"],
                    description=domain_data.get("description"),
                    sort_order=domain_data.get("sort_order", 0),
                    metadata=domain_data.get("metadata", {}),
                    created_by=user_id,
                    updated_by=user_id
                )
                self.db.add(domain)
                self.db.flush()
                
                # Import categories for this domain
                for category_data in domain_data.get("categories", []):
                    category = FrameworkCategory(
                        domain_id=domain.id,
                        category_id=category_data["category_id"],
                        name=category_data["name"],
                        description=category_data.get("description"),
                        sort_order=category_data.get("sort_order", 0),
                        metadata=category_data.get("metadata", {}),
                        created_by=user_id,
                        updated_by=user_id
                    )
                    self.db.add(category)
                    self.db.flush()
                    
                    # Import controls for this category
                    for control_data in category_data.get("controls", []):
                        control = FrameworkControl(
                            framework_id=framework.id,
                            domain_id=domain.id,
                            category_id=category.id,
                            control_id=control_data["control_id"],
                            name=control_data["name"],
                            description=control_data.get("description"),
                            objective=control_data.get("objective"),
                            implementation_guidance=control_data.get("implementation_guidance"),
                            control_type=control_data.get("control_type"),
                            automation_level=control_data.get("automation_level"),
                            risk_rating=control_data.get("risk_rating"),
                            priority=control_data.get("priority"),
                            assessment_criteria=control_data.get("assessment_criteria"),
                            testing_procedures=control_data.get("testing_procedures"),
                            evidence_requirements=control_data.get("evidence_requirements"),
                            sort_order=control_data.get("sort_order", 0),
                            references=control_data.get("references"),
                            tags=control_data.get("tags"),
                            metadata=control_data.get("metadata", {}),
                            created_by=user_id,
                            updated_by=user_id
                        )
                        self.db.add(control)
            
            self.db.commit()
            logger.info(f"Successfully imported framework: {framework.name} v{framework.version}")
            
            return framework
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to import framework data: {e}")
            raise
    
    async def bulk_update_implementations(
        self,
        updates: List[Dict[str, Any]],
        user_id: UUID
    ) -> Dict[str, Any]:
        """Bulk update control implementations"""
        
        results = {
            "updated": 0,
            "created": 0,
            "errors": []
        }
        
        try:
            for update_data in updates:
                try:
                    control_id = update_data["control_id"]
                    organization_id = update_data.get("organization_id")
                    
                    # Find existing implementation
                    implementation = self.db.query(ControlImplementation).filter(
                        and_(
                            ControlImplementation.control_id == control_id,
                            ControlImplementation.organization_id == organization_id,
                            ControlImplementation.deleted_at.is_(None)
                        )
                    ).first()
                    
                    if implementation:
                        # Update existing
                        for field, value in update_data.items():
                            if field not in ["control_id", "organization_id"] and hasattr(implementation, field):
                                setattr(implementation, field, value)
                        
                        implementation.updated_by = user_id
                        implementation.updated_at = datetime.utcnow()
                        results["updated"] += 1
                        
                    else:
                        # Create new
                        implementation = ControlImplementation(
                            **update_data,
                            created_by=user_id,
                            updated_by=user_id
                        )
                        self.db.add(implementation)
                        results["created"] += 1
                        
                except Exception as e:
                    results["errors"].append({
                        "control_id": update_data.get("control_id"),
                        "error": str(e)
                    })
            
            self.db.commit()
            logger.info(f"Bulk update completed: {results['updated']} updated, {results['created']} created")
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Bulk update failed: {e}")
            raise
        
        return results
    
    async def generate_compliance_report(
        self,
        framework_id: UUID,
        organization_id: Optional[UUID] = None,
        include_evidence: bool = False
    ) -> Dict[str, Any]:
        """Generate comprehensive compliance report"""
        
        # Get framework
        framework = self.db.query(CybersecurityFramework).filter(
            and_(
                CybersecurityFramework.id == framework_id,
                CybersecurityFramework.deleted_at.is_(None)
            )
        ).first()
        
        if not framework:
            raise ValueError("Framework not found")
        
        # Get all controls for this framework
        controls_query = self.db.query(FrameworkControl).filter(
            and_(
                FrameworkControl.framework_id == framework_id,
                FrameworkControl.deleted_at.is_(None)
            )
        )
        
        controls = controls_query.all()
        
        report = {
            "framework": {
                "id": str(framework.id),
                "name": framework.name,
                "type": framework.framework_type,
                "version": framework.version
            },
            "generated_at": datetime.utcnow().isoformat(),
            "organization_id": str(organization_id) if organization_id else None,
            "summary": {
                "total_controls": len(controls),
                "implemented": 0,
                "partially_implemented": 0,
                "not_implemented": 0,
                "verified": 0,
                "compliance_percentage": 0.0
            },
            "domains": [],
            "risk_analysis": {
                "critical_gaps": [],
                "high_risk_gaps": [],
                "medium_risk_gaps": []
            },
            "recommendations": []
        }
        
        # Group controls by domain
        domains_dict = {}
        
        for control in controls:
            # Get latest implementation
            impl_query = self.db.query(ControlImplementation).filter(
                and_(
                    ControlImplementation.control_id == control.id,
                    ControlImplementation.deleted_at.is_(None)
                )
            )
            
            if organization_id:
                impl_query = impl_query.filter(ControlImplementation.organization_id == organization_id)
            
            implementation = impl_query.order_by(ControlImplementation.updated_at.desc()).first()
            
            # Get latest assessment
            assessment = self.db.query(ControlAssessment).filter(
                and_(
                    ControlAssessment.control_id == control.id,
                    ControlAssessment.deleted_at.is_(None)
                )
            ).order_by(ControlAssessment.assessment_date.desc()).first()
            
            # Determine status
            status = "not_implemented"
            if implementation:
                status = implementation.status
            elif assessment:
                status = assessment.status
            
            # Update summary counts
            if status == "implemented":
                report["summary"]["implemented"] += 1
            elif status == "partially_implemented":
                report["summary"]["partially_implemented"] += 1
            elif status == "verified":
                report["summary"]["verified"] += 1
            else:
                report["summary"]["not_implemented"] += 1
            
            # Add to risk analysis if gap exists
            if status in ["not_implemented", "partially_implemented"] and control.risk_rating:
                gap_info = {
                    "control_id": control.control_id,
                    "name": control.name,
                    "risk_rating": control.risk_rating,
                    "status": status,
                    "gaps": implementation.gaps if implementation else ["No implementation found"]
                }
                
                if control.risk_rating == "critical":
                    report["risk_analysis"]["critical_gaps"].append(gap_info)
                elif control.risk_rating == "high":
                    report["risk_analysis"]["high_risk_gaps"].append(gap_info)
                elif control.risk_rating == "medium":
                    report["risk_analysis"]["medium_risk_gaps"].append(gap_info)
            
            # Group by domain
            domain_name = control.domain.name if control.domain else "Uncategorized"
            if domain_name not in domains_dict:
                domains_dict[domain_name] = {
                    "name": domain_name,
                    "controls": [],
                    "summary": {
                        "total": 0,
                        "implemented": 0,
                        "partially_implemented": 0,
                        "not_implemented": 0,
                        "verified": 0
                    }
                }
            
            control_info = {
                "control_id": control.control_id,
                "name": control.name,
                "status": status,
                "risk_rating": control.risk_rating,
                "last_assessed": assessment.assessment_date.isoformat() if assessment else None,
                "compliance_score": assessment.compliance_score if assessment else None
            }
            
            if include_evidence and implementation:
                control_info["evidence"] = implementation.evidence
                control_info["documentation"] = implementation.documentation
            
            domains_dict[domain_name]["controls"].append(control_info)
            domains_dict[domain_name]["summary"]["total"] += 1
            domains_dict[domain_name]["summary"][status] += 1
        
        # Calculate compliance percentage
        total_implemented = report["summary"]["implemented"] + report["summary"]["verified"]
        total_controls = report["summary"]["total_controls"]
        report["summary"]["compliance_percentage"] = (
            (total_implemented / total_controls * 100) if total_controls > 0 else 0
        )
        
        # Add domains to report
        report["domains"] = list(domains_dict.values())
        
        # Generate recommendations
        if report["summary"]["compliance_percentage"] < 50:
            report["recommendations"].append("Immediate action required - compliance below 50%")
        
        if len(report["risk_analysis"]["critical_gaps"]) > 0:
            report["recommendations"].append(
                f"Address {len(report['risk_analysis']['critical_gaps'])} critical gaps immediately"
            )
        
        if len(report["risk_analysis"]["high_risk_gaps"]) > 5:
            report["recommendations"].append("Focus on high-risk controls to improve security posture")
        
        return report
    
    async def suggest_control_mappings(
        self,
        source_framework_id: UUID,
        target_framework_id: UUID,
        confidence_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """Suggest control mappings between frameworks using ML/NLP techniques"""
        
        # Get controls from both frameworks
        source_controls = self.db.query(FrameworkControl).filter(
            and_(
                FrameworkControl.framework_id == source_framework_id,
                FrameworkControl.deleted_at.is_(None)
            )
        ).all()
        
        target_controls = self.db.query(FrameworkControl).filter(
            and_(
                FrameworkControl.framework_id == target_framework_id,
                FrameworkControl.deleted_at.is_(None)
            )
        ).all()
        
        mappings = []
        
        for source_control in source_controls:
            best_matches = []
            
            for target_control in target_controls:
                similarity = await self._calculate_advanced_similarity(source_control, target_control)
                
                if similarity >= confidence_threshold:
                    best_matches.append({
                        "target_control": {
                            "id": str(target_control.id),
                            "control_id": target_control.control_id,
                            "name": target_control.name
                        },
                        "similarity_score": similarity,
                        "mapping_rationale": self._generate_mapping_rationale(source_control, target_control)
                    })
            
            # Sort by similarity score
            best_matches.sort(key=lambda x: x["similarity_score"], reverse=True)
            
            if best_matches:
                mappings.append({
                    "source_control": {
                        "id": str(source_control.id),
                        "control_id": source_control.control_id,
                        "name": source_control.name
                    },
                    "suggested_mappings": best_matches[:3]  # Top 3 matches
                })
        
        return mappings
    
    async def _calculate_advanced_similarity(
        self,
        control1: FrameworkControl,
        control2: FrameworkControl
    ) -> float:
        """Calculate advanced similarity using multiple factors"""
        
        # Text similarity (simplified - in production use proper NLP)
        text_sim = self._text_similarity(
            f"{control1.name} {control1.description or ''} {control1.objective or ''}",
            f"{control2.name} {control2.description or ''} {control2.objective or ''}"
        )
        
        # Control type similarity
        type_sim = 1.0 if control1.control_type == control2.control_type else 0.0
        
        # Risk rating similarity
        risk_sim = 1.0 if control1.risk_rating == control2.risk_rating else 0.5
        
        # Weighted combination
        similarity = (text_sim * 0.6) + (type_sim * 0.3) + (risk_sim * 0.1)
        
        return min(similarity, 1.0)
    
    def _text_similarity(self, text1: str, text2: str) -> float:
        """Simple text similarity calculation"""
        
        # Convert to lowercase and split into words
        words1 = set(re.findall(r'\w+', text1.lower()))
        words2 = set(re.findall(r'\w+', text2.lower()))
        
        # Calculate Jaccard similarity
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    def _generate_mapping_rationale(
        self,
        source_control: FrameworkControl,
        target_control: FrameworkControl
    ) -> str:
        """Generate human-readable rationale for control mapping"""
        
        reasons = []
        
        if source_control.control_type == target_control.control_type:
            reasons.append(f"Same control type ({source_control.control_type})")
        
        if source_control.risk_rating == target_control.risk_rating:
            reasons.append(f"Same risk rating ({source_control.risk_rating})")
        
        # Check for common keywords
        common_keywords = self._find_common_keywords(source_control, target_control)
        if common_keywords:
            reasons.append(f"Common security concepts: {', '.join(common_keywords[:3])}")
        
        return "; ".join(reasons) if reasons else "Similar objectives and scope"
    
    def _find_common_keywords(
        self,
        control1: FrameworkControl,
        control2: FrameworkControl
    ) -> List[str]:
        """Find common security keywords between controls"""
        
        security_keywords = [
            "access", "authentication", "authorization", "encryption", "monitoring",
            "logging", "backup", "recovery", "incident", "vulnerability", "patch",
            "configuration", "network", "firewall", "antivirus", "malware", "audit",
            "compliance", "risk", "security", "privacy", "data", "system"
        ]
        
        text1 = f"{control1.name} {control1.description or ''} {control1.objective or ''}".lower()
        text2 = f"{control2.name} {control2.description or ''} {control2.objective or ''}".lower()
        
        common = []
        for keyword in security_keywords:
            if keyword in text1 and keyword in text2:
                common.append(keyword)
        
        return common
