"""
Framework Data Import Service
Automated import system for cybersecurity framework data from official sources
"""

import asyncio
import json
import xml.etree.ElementTree as ET
import pandas as pd
import logging
import requests
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from uuid import UUID
from pathlib import Path
import tempfile
import zipfile
from io import BytesIO

from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.db.models.cybersecurity_frameworks import (
    CybersecurityFramework,
    FrameworkDomain,
    FrameworkCategory,
    FrameworkControl,
    FrameworkType
)
from app.services.cybersecurity_frameworks_service import CybersecurityFrameworksService
from app.core.config import settings

logger = logging.getLogger(__name__)


class FrameworkImportService:
    """Service for importing cybersecurity framework data from official sources"""
    
    def __init__(self, db: Session):
        self.db = db
        self.frameworks_service = CybersecurityFrameworksService(db)
        
        # Official data source URLs
        self.data_sources = {
            'nist_csf_2_0': {
                'url': 'https://csrc.nist.gov/CSRC/media/Publications/cybersecurity-framework/2.0/final/documents/csf-2.0-core.json',
                'format': 'json',
                'parser': self._parse_nist_csf_json
            },
            'iso_27001_2022': {
                'url': 'https://www.iso.org/standard/27001-2022-controls.xml',  # Hypothetical URL
                'format': 'xml',
                'parser': self._parse_iso_27001_xml
            },
            'cis_controls_v8': {
                'url': 'https://www.cisecurity.org/controls/v8/controls-spreadsheet.xlsx',  # Hypothetical URL
                'format': 'excel',
                'parser': self._parse_cis_controls_excel
            },
            'isf_2022': {
                'url': 'https://www.securityforum.org/isf-2022-controls.json',  # Hypothetical URL
                'format': 'json',
                'parser': self._parse_isf_json
            }
        }
    
    async def import_framework_from_source(
        self,
        framework_type: str,
        user_id: UUID,
        force_update: bool = False
    ) -> Dict[str, Any]:
        """Import framework data from official source"""
        
        if framework_type not in self.data_sources:
            raise ValueError(f"Unsupported framework type: {framework_type}")
        
        source_config = self.data_sources[framework_type]
        
        try:
            # Check if framework already exists
            existing_framework = self.db.query(CybersecurityFramework).filter(
                and_(
                    CybersecurityFramework.framework_type == framework_type,
                    CybersecurityFramework.deleted_at.is_(None)
                )
            ).first()
            
            if existing_framework and not force_update:
                return {
                    "status": "skipped",
                    "message": f"Framework {framework_type} already exists. Use force_update=True to reimport.",
                    "framework_id": str(existing_framework.id)
                }
            
            # Download data from source
            logger.info(f"Downloading {framework_type} data from {source_config['url']}")
            raw_data = await self._download_framework_data(source_config['url'])
            
            # Parse data based on format
            logger.info(f"Parsing {framework_type} data")
            parsed_data = await source_config['parser'](raw_data)
            
            # Validate parsed data
            validation_result = self._validate_framework_data(parsed_data)
            if not validation_result['valid']:
                raise ValueError(f"Data validation failed: {validation_result['errors']}")
            
            # Import into database
            if existing_framework and force_update:
                # Soft delete existing framework
                existing_framework.deleted_at = datetime.utcnow()
                self.db.commit()
            
            framework = await self.frameworks_service.import_framework_data(
                FrameworkType(framework_type),
                parsed_data,
                user_id
            )
            
            # Generate import report
            report = await self._generate_import_report(framework, parsed_data)
            
            logger.info(f"Successfully imported {framework_type}: {framework.name}")
            
            return {
                "status": "success",
                "message": f"Successfully imported {framework_type}",
                "framework_id": str(framework.id),
                "framework_name": framework.name,
                "import_report": report
            }
            
        except Exception as e:
            logger.error(f"Failed to import {framework_type}: {e}")
            return {
                "status": "error",
                "message": f"Failed to import {framework_type}: {str(e)}",
                "error_details": str(e)
            }
    
    async def _download_framework_data(self, url: str) -> bytes:
        """Download framework data from URL"""
        
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            return response.content
        except requests.RequestException as e:
            raise Exception(f"Failed to download data from {url}: {e}")
    
    async def _parse_nist_csf_json(self, raw_data: bytes) -> Dict[str, Any]:
        """Parse NIST CSF 2.0 JSON data"""
        
        try:
            data = json.loads(raw_data.decode('utf-8'))
            
            framework_data = {
                "name": "NIST Cybersecurity Framework 2.0",
                "framework_type": "nist_csf_2_0",
                "version": "2.0",
                "description": "A voluntary framework consisting of standards, guidelines, and practices to promote the protection of critical infrastructure",
                "authority": "National Institute of Standards and Technology",
                "publication_date": datetime(2024, 2, 26),
                "effective_date": datetime(2024, 2, 26),
                "metadata": {
                    "source_url": "https://csrc.nist.gov/",
                    "import_date": datetime.utcnow().isoformat(),
                    "total_functions": 0,
                    "total_categories": 0,
                    "total_subcategories": 0
                },
                "domains": []
            }
            
            # Parse functions (domains)
            functions = data.get('functions', [])
            
            for func_data in functions:
                domain = {
                    "domain_id": func_data.get('id', ''),
                    "name": func_data.get('name', ''),
                    "description": func_data.get('description', ''),
                    "sort_order": len(framework_data["domains"]),
                    "metadata": {
                        "function_type": func_data.get('type', ''),
                        "outcomes": func_data.get('outcomes', [])
                    },
                    "categories": []
                }
                
                # Parse categories
                categories = func_data.get('categories', [])
                
                for cat_data in categories:
                    category = {
                        "category_id": cat_data.get('id', ''),
                        "name": cat_data.get('name', ''),
                        "description": cat_data.get('description', ''),
                        "sort_order": len(domain["categories"]),
                        "metadata": {
                            "outcomes": cat_data.get('outcomes', [])
                        },
                        "controls": []
                    }
                    
                    # Parse subcategories (controls)
                    subcategories = cat_data.get('subcategories', [])
                    
                    for subcat_data in subcategories:
                        control = {
                            "control_id": subcat_data.get('id', ''),
                            "name": subcat_data.get('name', ''),
                            "description": subcat_data.get('description', ''),
                            "objective": subcat_data.get('outcome', ''),
                            "implementation_guidance": subcat_data.get('guidance', ''),
                            "control_type": self._determine_nist_control_type(subcat_data),
                            "automation_level": "manual",  # Default
                            "risk_rating": self._determine_nist_risk_rating(subcat_data),
                            "priority": "medium",  # Default
                            "sort_order": len(category["controls"]),
                            "references": subcat_data.get('references', []),
                            "tags": self._extract_nist_tags(subcat_data),
                            "metadata": {
                                "informative_references": subcat_data.get('informative_references', []),
                                "examples": subcat_data.get('examples', [])
                            }
                        }
                        
                        category["controls"].append(control)
                    
                    domain["categories"].append(category)
                
                framework_data["domains"].append(domain)
            
            # Update metadata counts
            framework_data["metadata"]["total_functions"] = len(framework_data["domains"])
            framework_data["metadata"]["total_categories"] = sum(len(d["categories"]) for d in framework_data["domains"])
            framework_data["metadata"]["total_subcategories"] = sum(
                len(c["controls"]) for d in framework_data["domains"] for c in d["categories"]
            )
            
            return framework_data
            
        except (json.JSONDecodeError, KeyError) as e:
            raise Exception(f"Failed to parse NIST CSF JSON: {e}")
    
    async def _parse_iso_27001_xml(self, raw_data: bytes) -> Dict[str, Any]:
        """Parse ISO 27001:2022 XML data"""
        
        try:
            root = ET.fromstring(raw_data.decode('utf-8'))
            
            framework_data = {
                "name": "ISO/IEC 27001:2022 Information Security Management",
                "framework_type": "iso_27001_2022",
                "version": "2022",
                "description": "International standard for information security management systems (ISMS)",
                "authority": "International Organization for Standardization",
                "publication_date": datetime(2022, 10, 25),
                "effective_date": datetime(2022, 10, 25),
                "metadata": {
                    "source_url": "https://www.iso.org/",
                    "import_date": datetime.utcnow().isoformat(),
                    "total_domains": 0,
                    "total_controls": 0
                },
                "domains": []
            }
            
            # Parse control domains
            for domain_elem in root.findall('.//domain'):
                domain = {
                    "domain_id": domain_elem.get('id', ''),
                    "name": domain_elem.find('name').text if domain_elem.find('name') is not None else '',
                    "description": domain_elem.find('description').text if domain_elem.find('description') is not None else '',
                    "sort_order": len(framework_data["domains"]),
                    "categories": []
                }
                
                # Parse controls within domain
                for control_elem in domain_elem.findall('.//control'):
                    # For ISO 27001, controls are direct children of domains
                    category = {
                        "category_id": f"{domain['domain_id']}.CAT",
                        "name": f"{domain['name']} Controls",
                        "description": f"Controls for {domain['name']}",
                        "sort_order": 0,
                        "controls": []
                    }
                    
                    control = {
                        "control_id": control_elem.get('id', ''),
                        "name": control_elem.find('title').text if control_elem.find('title') is not None else '',
                        "description": control_elem.find('description').text if control_elem.find('description') is not None else '',
                        "objective": control_elem.find('objective').text if control_elem.find('objective') is not None else '',
                        "implementation_guidance": control_elem.find('guidance').text if control_elem.find('guidance') is not None else '',
                        "control_type": self._determine_iso_control_type(control_elem),
                        "automation_level": "manual",
                        "risk_rating": "medium",  # Default
                        "priority": "medium",
                        "sort_order": len(category["controls"]),
                        "tags": self._extract_iso_tags(control_elem),
                        "metadata": {
                            "control_type_iso": control_elem.get('type', ''),
                            "implementation_level": control_elem.get('level', '')
                        }
                    }
                    
                    category["controls"].append(control)
                
                if category["controls"]:  # Only add category if it has controls
                    domain["categories"].append(category)
                
                framework_data["domains"].append(domain)
            
            # Update metadata
            framework_data["metadata"]["total_domains"] = len(framework_data["domains"])
            framework_data["metadata"]["total_controls"] = sum(
                len(c["controls"]) for d in framework_data["domains"] for c in d["categories"]
            )
            
            return framework_data
            
        except ET.ParseError as e:
            raise Exception(f"Failed to parse ISO 27001 XML: {e}")
    
    async def _parse_cis_controls_excel(self, raw_data: bytes) -> Dict[str, Any]:
        """Parse CIS Controls v8 Excel data"""
        
        try:
            # Read Excel file
            df = pd.read_excel(BytesIO(raw_data), sheet_name='Controls')
            
            framework_data = {
                "name": "CIS Controls Version 8",
                "framework_type": "cis_controls_v8",
                "version": "8.0",
                "description": "A prioritized set of actions for cyber defense that provide specific and actionable ways to stop today's most pervasive and dangerous attacks",
                "authority": "Center for Internet Security",
                "publication_date": datetime(2021, 5, 18),
                "effective_date": datetime(2021, 5, 18),
                "metadata": {
                    "source_url": "https://www.cisecurity.org/",
                    "import_date": datetime.utcnow().isoformat(),
                    "total_controls": 0,
                    "total_safeguards": 0
                },
                "domains": []
            }
            
            # Group by control number
            for control_num in df['Control'].unique():
                control_data = df[df['Control'] == control_num].iloc[0]
                
                domain = {
                    "domain_id": f"CIS{control_num}",
                    "name": control_data['Control Title'],
                    "description": control_data['Control Description'],
                    "sort_order": int(control_num) - 1,
                    "categories": []
                }
                
                # Create category for safeguards
                category = {
                    "category_id": f"CIS{control_num}.1",
                    "name": f"CIS Control {control_num} Safeguards",
                    "description": f"Safeguards for {control_data['Control Title']}",
                    "sort_order": 0,
                    "controls": []
                }
                
                # Parse safeguards for this control
                safeguards = df[df['Control'] == control_num]
                
                for _, safeguard in safeguards.iterrows():
                    control = {
                        "control_id": f"CIS{control_num}.{safeguard['Safeguard']}",
                        "name": safeguard['Safeguard Title'],
                        "description": safeguard['Safeguard Description'],
                        "objective": safeguard.get('Asset Type', ''),
                        "implementation_guidance": safeguard.get('Implementation Guidance', ''),
                        "control_type": self._determine_cis_control_type(safeguard),
                        "automation_level": self._determine_cis_automation_level(safeguard),
                        "risk_rating": self._determine_cis_risk_rating(control_num),
                        "priority": self._determine_cis_priority(control_num),
                        "sort_order": int(safeguard['Safeguard']) - 1,
                        "tags": self._extract_cis_tags(safeguard),
                        "metadata": {
                            "implementation_group": safeguard.get('Implementation Group', ''),
                            "asset_type": safeguard.get('Asset Type', ''),
                            "security_function": safeguard.get('Security Function', '')
                        }
                    }
                    
                    category["controls"].append(control)
                
                domain["categories"].append(category)
                framework_data["domains"].append(domain)
            
            # Update metadata
            framework_data["metadata"]["total_controls"] = len(framework_data["domains"])
            framework_data["metadata"]["total_safeguards"] = sum(
                len(c["controls"]) for d in framework_data["domains"] for c in d["categories"]
            )
            
            return framework_data
            
        except Exception as e:
            raise Exception(f"Failed to parse CIS Controls Excel: {e}")
    
    async def _parse_isf_json(self, raw_data: bytes) -> Dict[str, Any]:
        """Parse ISF 2022 JSON data"""
        
        try:
            data = json.loads(raw_data.decode('utf-8'))
            
            framework_data = {
                "name": "Information Security Forum Standard of Good Practice 2022",
                "framework_type": "isf_2022",
                "version": "2022",
                "description": "The ISF Standard of Good Practice provides a comprehensive framework for information security management",
                "authority": "Information Security Forum",
                "publication_date": datetime(2022, 1, 1),
                "effective_date": datetime(2022, 1, 1),
                "metadata": {
                    "source_url": "https://www.securityforum.org/",
                    "import_date": datetime.utcnow().isoformat(),
                    "total_areas": 0,
                    "total_controls": 0
                },
                "domains": []
            }
            
            # Parse security areas
            areas = data.get('security_areas', [])
            
            for area_data in areas:
                domain = {
                    "domain_id": area_data.get('id', ''),
                    "name": area_data.get('name', ''),
                    "description": area_data.get('description', ''),
                    "sort_order": len(framework_data["domains"]),
                    "categories": []
                }
                
                # Parse sub-areas as categories
                sub_areas = area_data.get('sub_areas', [])
                
                for sub_area_data in sub_areas:
                    category = {
                        "category_id": sub_area_data.get('id', ''),
                        "name": sub_area_data.get('name', ''),
                        "description": sub_area_data.get('description', ''),
                        "sort_order": len(domain["categories"]),
                        "controls": []
                    }
                    
                    # Parse controls
                    controls = sub_area_data.get('controls', [])
                    
                    for control_data in controls:
                        control = {
                            "control_id": control_data.get('id', ''),
                            "name": control_data.get('name', ''),
                            "description": control_data.get('description', ''),
                            "objective": control_data.get('objective', ''),
                            "implementation_guidance": control_data.get('guidance', ''),
                            "control_type": control_data.get('type', 'strategic'),
                            "automation_level": "manual",
                            "risk_rating": control_data.get('risk_level', 'medium'),
                            "priority": control_data.get('priority', 'medium'),
                            "sort_order": len(category["controls"]),
                            "tags": control_data.get('tags', []),
                            "metadata": {
                                "maturity_level": control_data.get('maturity_level', ''),
                                "business_impact": control_data.get('business_impact', '')
                            }
                        }
                        
                        category["controls"].append(control)
                    
                    domain["categories"].append(category)
                
                framework_data["domains"].append(domain)
            
            # Update metadata
            framework_data["metadata"]["total_areas"] = len(framework_data["domains"])
            framework_data["metadata"]["total_controls"] = sum(
                len(c["controls"]) for d in framework_data["domains"] for c in d["categories"]
            )
            
            return framework_data
            
        except (json.JSONDecodeError, KeyError) as e:
            raise Exception(f"Failed to parse ISF JSON: {e}")
    
    def _validate_framework_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate parsed framework data"""
        
        errors = []
        
        # Required fields
        required_fields = ['name', 'framework_type', 'version', 'authority']
        for field in required_fields:
            if not data.get(field):
                errors.append(f"Missing required field: {field}")
        
        # Validate domains
        domains = data.get('domains', [])
        if not domains:
            errors.append("No domains found in framework data")
        
        for i, domain in enumerate(domains):
            if not domain.get('domain_id'):
                errors.append(f"Domain {i} missing domain_id")
            if not domain.get('name'):
                errors.append(f"Domain {i} missing name")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    async def _generate_import_report(self, framework: CybersecurityFramework, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate import report"""
        
        # Count imported items
        domains_count = len(data.get('domains', []))
        categories_count = sum(len(d.get('categories', [])) for d in data.get('domains', []))
        controls_count = sum(
            len(c.get('controls', [])) 
            for d in data.get('domains', []) 
            for c in d.get('categories', [])
        )
        
        return {
            "framework_id": str(framework.id),
            "framework_name": framework.name,
            "import_timestamp": datetime.utcnow().isoformat(),
            "imported_counts": {
                "domains": domains_count,
                "categories": categories_count,
                "controls": controls_count
            },
            "source_metadata": data.get('metadata', {}),
            "validation_status": "passed"
        }

    # Helper methods for determining control characteristics

    def _determine_nist_control_type(self, subcat_data: Dict[str, Any]) -> str:
        """Determine control type for NIST CSF controls"""

        name = subcat_data.get('name', '').lower()
        description = subcat_data.get('description', '').lower()

        if any(keyword in name + description for keyword in ['identify', 'inventory', 'assess']):
            return 'detective'
        elif any(keyword in name + description for keyword in ['protect', 'implement', 'configure']):
            return 'preventive'
        elif any(keyword in name + description for keyword in ['detect', 'monitor', 'analyze']):
            return 'detective'
        elif any(keyword in name + description for keyword in ['respond', 'contain', 'mitigate']):
            return 'corrective'
        elif any(keyword in name + description for keyword in ['recover', 'restore', 'improve']):
            return 'corrective'
        else:
            return 'preventive'  # Default

    def _determine_nist_risk_rating(self, subcat_data: Dict[str, Any]) -> str:
        """Determine risk rating for NIST CSF controls"""

        # Map based on function and criticality
        control_id = subcat_data.get('id', '')

        if control_id.startswith('ID.'):
            return 'high'  # Identify function is foundational
        elif control_id.startswith('PR.'):
            return 'high'  # Protect function is critical
        elif control_id.startswith('DE.'):
            return 'medium'  # Detect function
        elif control_id.startswith('RS.'):
            return 'medium'  # Respond function
        elif control_id.startswith('RC.'):
            return 'medium'  # Recover function
        else:
            return 'medium'  # Default

    def _extract_nist_tags(self, subcat_data: Dict[str, Any]) -> List[str]:
        """Extract tags for NIST CSF controls"""

        tags = []

        # Add function-based tags
        control_id = subcat_data.get('id', '')
        if control_id.startswith('ID.'):
            tags.append('identify')
        elif control_id.startswith('PR.'):
            tags.append('protect')
        elif control_id.startswith('DE.'):
            tags.append('detect')
        elif control_id.startswith('RS.'):
            tags.append('respond')
        elif control_id.startswith('RC.'):
            tags.append('recover')

        # Extract keywords from description
        description = subcat_data.get('description', '').lower()
        keyword_tags = {
            'access': 'access_control',
            'authentication': 'authentication',
            'encryption': 'encryption',
            'monitoring': 'monitoring',
            'backup': 'backup',
            'incident': 'incident_response',
            'vulnerability': 'vulnerability_management',
            'network': 'network_security',
            'data': 'data_protection'
        }

        for keyword, tag in keyword_tags.items():
            if keyword in description:
                tags.append(tag)

        return list(set(tags))  # Remove duplicates

    def _determine_iso_control_type(self, control_elem) -> str:
        """Determine control type for ISO 27001 controls"""

        control_type = control_elem.get('type', '').lower()

        if 'preventive' in control_type or 'protective' in control_type:
            return 'preventive'
        elif 'detective' in control_type or 'monitoring' in control_type:
            return 'detective'
        elif 'corrective' in control_type or 'responsive' in control_type:
            return 'corrective'
        else:
            return 'preventive'  # Default

    def _extract_iso_tags(self, control_elem) -> List[str]:
        """Extract tags for ISO 27001 controls"""

        tags = []

        # Extract from control ID pattern
        control_id = control_elem.get('id', '')
        if control_id.startswith('A.5'):
            tags.append('organizational')
        elif control_id.startswith('A.6'):
            tags.append('people')
        elif control_id.startswith('A.7'):
            tags.append('physical')
        elif control_id.startswith('A.8'):
            tags.append('technological')

        # Extract from title and description
        title = control_elem.find('title').text if control_elem.find('title') is not None else ''
        description = control_elem.find('description').text if control_elem.find('description') is not None else ''

        text_content = (title + ' ' + description).lower()

        keyword_tags = {
            'access': 'access_control',
            'authentication': 'authentication',
            'encryption': 'cryptography',
            'network': 'network_security',
            'incident': 'incident_management',
            'backup': 'backup_recovery',
            'audit': 'audit_logging',
            'supplier': 'supplier_management',
            'asset': 'asset_management'
        }

        for keyword, tag in keyword_tags.items():
            if keyword in text_content:
                tags.append(tag)

        return list(set(tags))

    def _determine_cis_control_type(self, safeguard) -> str:
        """Determine control type for CIS Controls"""

        security_function = safeguard.get('Security Function', '').lower()

        if 'protect' in security_function:
            return 'preventive'
        elif 'detect' in security_function:
            return 'detective'
        elif 'respond' in security_function:
            return 'corrective'
        elif 'recover' in security_function:
            return 'corrective'
        else:
            return 'preventive'  # Default

    def _determine_cis_automation_level(self, safeguard) -> str:
        """Determine automation level for CIS Controls"""

        title = safeguard.get('Safeguard Title', '').lower()
        description = safeguard.get('Safeguard Description', '').lower()

        if any(keyword in title + description for keyword in ['automated', 'automatic', 'tool']):
            return 'automated'
        elif any(keyword in title + description for keyword in ['semi-automated', 'assisted']):
            return 'semi_automated'
        else:
            return 'manual'

    def _determine_cis_risk_rating(self, control_num: int) -> str:
        """Determine risk rating for CIS Controls based on control number"""

        # CIS Controls 1-6 are typically foundational (high priority)
        if control_num <= 6:
            return 'high'
        # Controls 7-16 are organizational (medium priority)
        elif control_num <= 16:
            return 'medium'
        # Controls 17-18 are advanced (medium priority)
        else:
            return 'medium'

    def _determine_cis_priority(self, control_num: int) -> str:
        """Determine priority for CIS Controls"""

        # Implementation Groups mapping
        if control_num <= 6:
            return 'high'  # IG1 controls
        elif control_num <= 16:
            return 'medium'  # IG2 controls
        else:
            return 'medium'  # IG3 controls

    def _extract_cis_tags(self, safeguard) -> List[str]:
        """Extract tags for CIS Controls"""

        tags = []

        # Add implementation group tag
        impl_group = safeguard.get('Implementation Group', '')
        if impl_group:
            tags.append(f'ig{impl_group.lower()}')

        # Add asset type tag
        asset_type = safeguard.get('Asset Type', '').lower()
        if asset_type:
            tags.append(asset_type.replace(' ', '_'))

        # Add security function tag
        security_function = safeguard.get('Security Function', '').lower()
        if security_function:
            tags.append(security_function.replace(' ', '_'))

        # Extract from title
        title = safeguard.get('Safeguard Title', '').lower()

        keyword_tags = {
            'inventory': 'asset_inventory',
            'software': 'software_management',
            'configuration': 'configuration_management',
            'access': 'access_control',
            'account': 'account_management',
            'logging': 'audit_logging',
            'email': 'email_security',
            'malware': 'malware_defense',
            'network': 'network_security',
            'data': 'data_protection',
            'backup': 'data_recovery',
            'boundary': 'network_boundary',
            'monitoring': 'security_monitoring',
            'awareness': 'security_awareness',
            'service': 'service_provider',
            'application': 'application_security',
            'incident': 'incident_response',
            'penetration': 'penetration_testing'
        }

        for keyword, tag in keyword_tags.items():
            if keyword in title:
                tags.append(tag)

        return list(set(tags))

    async def import_framework_from_file(
        self,
        framework_type: str,
        file_path: str,
        user_id: UUID,
        force_update: bool = False
    ) -> Dict[str, Any]:
        """Import framework data from local file"""

        if framework_type not in self.data_sources:
            raise ValueError(f"Unsupported framework type: {framework_type}")

        source_config = self.data_sources[framework_type]

        try:
            # Read file data
            with open(file_path, 'rb') as f:
                raw_data = f.read()

            # Parse data
            parsed_data = await source_config['parser'](raw_data)

            # Validate data
            validation_result = self._validate_framework_data(parsed_data)
            if not validation_result['valid']:
                raise ValueError(f"Data validation failed: {validation_result['errors']}")

            # Check if framework exists
            existing_framework = self.db.query(CybersecurityFramework).filter(
                and_(
                    CybersecurityFramework.framework_type == framework_type,
                    CybersecurityFramework.deleted_at.is_(None)
                )
            ).first()

            if existing_framework and not force_update:
                return {
                    "status": "skipped",
                    "message": f"Framework {framework_type} already exists. Use force_update=True to reimport.",
                    "framework_id": str(existing_framework.id)
                }

            # Import data
            if existing_framework and force_update:
                existing_framework.deleted_at = datetime.utcnow()
                self.db.commit()

            framework = await self.frameworks_service.import_framework_data(
                FrameworkType(framework_type),
                parsed_data,
                user_id
            )

            # Generate report
            report = await self._generate_import_report(framework, parsed_data)

            return {
                "status": "success",
                "message": f"Successfully imported {framework_type} from file",
                "framework_id": str(framework.id),
                "framework_name": framework.name,
                "import_report": report
            }

        except Exception as e:
            logger.error(f"Failed to import {framework_type} from file: {e}")
            return {
                "status": "error",
                "message": f"Failed to import {framework_type} from file: {str(e)}",
                "error_details": str(e)
            }

    async def get_available_frameworks(self) -> List[Dict[str, Any]]:
        """Get list of available frameworks for import"""

        available_frameworks = []

        for framework_type, config in self.data_sources.items():
            # Check if framework already exists
            existing = self.db.query(CybersecurityFramework).filter(
                and_(
                    CybersecurityFramework.framework_type == framework_type,
                    CybersecurityFramework.deleted_at.is_(None)
                )
            ).first()

            available_frameworks.append({
                "framework_type": framework_type,
                "source_url": config['url'],
                "format": config['format'],
                "status": "imported" if existing else "available",
                "last_imported": existing.created_at.isoformat() if existing else None,
                "framework_id": str(existing.id) if existing else None
            })

        return available_frameworks

    async def validate_import_file(self, framework_type: str, file_path: str) -> Dict[str, Any]:
        """Validate an import file without importing"""

        if framework_type not in self.data_sources:
            return {
                "valid": False,
                "errors": [f"Unsupported framework type: {framework_type}"]
            }

        source_config = self.data_sources[framework_type]

        try:
            # Read and parse file
            with open(file_path, 'rb') as f:
                raw_data = f.read()

            parsed_data = await source_config['parser'](raw_data)

            # Validate parsed data
            validation_result = self._validate_framework_data(parsed_data)

            if validation_result['valid']:
                # Add preview information
                domains_count = len(parsed_data.get('domains', []))
                categories_count = sum(len(d.get('categories', [])) for d in parsed_data.get('domains', []))
                controls_count = sum(
                    len(c.get('controls', []))
                    for d in parsed_data.get('domains', [])
                    for c in d.get('categories', [])
                )

                validation_result['preview'] = {
                    "framework_name": parsed_data.get('name'),
                    "version": parsed_data.get('version'),
                    "authority": parsed_data.get('authority'),
                    "domains_count": domains_count,
                    "categories_count": categories_count,
                    "controls_count": controls_count
                }

            return validation_result

        except Exception as e:
            return {
                "valid": False,
                "errors": [f"Failed to validate file: {str(e)}"]
            }
