"""Main API router for Blast-Radius Security Tool."""

from fastapi import APIRouter

from app.api.v1 import (
    assets,
    auth,
    discovery,
    robust_assets,
    attack_paths,
    threat_modeling,
    mitre_attack,
    users,
    realtime,
    cybersecurity_frameworks
)

api_router = APIRouter()

# Include all API v1 routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(assets.router, prefix="/assets", tags=["assets"])
api_router.include_router(robust_assets.router, prefix="/robust-assets", tags=["robust-assets"])
api_router.include_router(discovery.router, prefix="/discovery", tags=["discovery"])
api_router.include_router(attack_paths.router, prefix="/attack-paths", tags=["attack-paths"])
api_router.include_router(threat_modeling.router, prefix="/threat-modeling", tags=["threat-modeling"])
api_router.include_router(mitre_attack.router, prefix="/mitre-attack", tags=["mitre-attack"])
api_router.include_router(realtime.router, prefix="/realtime", tags=["real-time-monitoring"])
api_router.include_router(cybersecurity_frameworks.router, prefix="/frameworks", tags=["cybersecurity-frameworks"])


@api_router.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "blast-radius-security-tool",
        "version": "1.0.0",
        "features": [
            "asset-discovery",
            "attack-path-analysis",
            "blast-radius-calculation",
            "robust-asset-management",
            "mitre-attack-integration",
            "real-time-monitoring",
            "cybersecurity-frameworks"
        ]
    }


@api_router.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Blast-Radius Security Tool API",
        "version": "1.0.0",
        "documentation": "/docs",
        "health": "/api/v1/health",
        "endpoints": {
            "authentication": "/api/v1/auth",
            "users": "/api/v1/users",
            "assets": "/api/v1/assets",
            "robust_assets": "/api/v1/robust-assets",
            "discovery": "/api/v1/discovery",
            "attack_paths": "/api/v1/attack-paths",
            "mitre_attack": "/api/v1/mitre-attack",
            "real_time_monitoring": "/api/v1/realtime",
            "cybersecurity_frameworks": "/api/v1/frameworks",
            "websocket": "/api/v1/realtime/ws"
        }
    }
