"""
Cybersecurity Frameworks API Endpoints
Provides comprehensive REST API for managing cybersecurity frameworks including:
- ISF 2022, NIST CSF 2.0, ISO/IEC 27001:2022, CIS Controls v8
- Framework mapping and cross-references
- Advanced search and analytics
- Implementation tracking and assessment
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, text

from app.api.deps import get_db, get_current_active_user
from app.db.models.user import User
from app.db.models.cybersecurity_frameworks import (
    CybersecurityFramework,
    FrameworkDomain,
    FrameworkCategory,
    FrameworkControl,
    ControlImplementation,
    ControlAssessment
)
from app.schemas.cybersecurity_frameworks import (
    # Framework schemas
    CybersecurityFrameworkCreate,
    CybersecurityFrameworkUpdate,
    CybersecurityFrameworkResponse,
    # Domain schemas
    FrameworkDomainCreate,
    FrameworkDomainUpdate,
    FrameworkDomainResponse,
    # Category schemas
    FrameworkCategoryCreate,
    FrameworkCategoryUpdate,
    FrameworkCategoryResponse,
    # Control schemas
    FrameworkControlCreate,
    FrameworkControlUpdate,
    FrameworkControlResponse,
    # Implementation schemas
    ControlImplementationCreate,
    ControlImplementationUpdate,
    ControlImplementationResponse,
    # Assessment schemas
    ControlAssessmentCreate,
    ControlAssessmentUpdate,
    ControlAssessmentResponse,
    # Search and analytics
    FrameworkSearchRequest,
    FrameworkSearchResponse,
    FrameworkAnalyticsRequest,
    FrameworkAnalyticsResponse,
    ComplianceGapAnalysis,
    FrameworkTypeEnum,
    ControlStatusEnum
)

router = APIRouter()


# Framework Management Endpoints
@router.get("/frameworks", response_model=List[CybersecurityFrameworkResponse])
async def list_frameworks(
    framework_type: Optional[FrameworkTypeEnum] = None,
    status: Optional[str] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """List all cybersecurity frameworks with optional filtering"""
    
    query = db.query(CybersecurityFramework).filter(CybersecurityFramework.deleted_at.is_(None))
    
    if framework_type:
        query = query.filter(CybersecurityFramework.framework_type == framework_type.value)
    
    if status:
        query = query.filter(CybersecurityFramework.status == status)
    
    frameworks = query.offset(skip).limit(limit).all()
    return frameworks


@router.post("/frameworks", response_model=CybersecurityFrameworkResponse, status_code=status.HTTP_201_CREATED)
async def create_framework(
    framework: CybersecurityFrameworkCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new cybersecurity framework"""
    
    # Check if framework already exists
    existing = db.query(CybersecurityFramework).filter(
        and_(
            CybersecurityFramework.name == framework.name,
            CybersecurityFramework.version == framework.version,
            CybersecurityFramework.deleted_at.is_(None)
        )
    ).first()
    
    if existing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Framework {framework.name} version {framework.version} already exists"
        )
    
    db_framework = CybersecurityFramework(
        **framework.dict(),
        created_by=current_user.id,
        updated_by=current_user.id
    )
    
    db.add(db_framework)
    db.commit()
    db.refresh(db_framework)
    
    return db_framework


@router.get("/frameworks/{framework_id}", response_model=CybersecurityFrameworkResponse)
async def get_framework(
    framework_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get a specific cybersecurity framework by ID"""
    
    framework = db.query(CybersecurityFramework).filter(
        and_(
            CybersecurityFramework.id == framework_id,
            CybersecurityFramework.deleted_at.is_(None)
        )
    ).first()
    
    if not framework:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Framework not found"
        )
    
    return framework


@router.put("/frameworks/{framework_id}", response_model=CybersecurityFrameworkResponse)
async def update_framework(
    framework_id: UUID,
    framework_update: CybersecurityFrameworkUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Update a cybersecurity framework"""
    
    framework = db.query(CybersecurityFramework).filter(
        and_(
            CybersecurityFramework.id == framework_id,
            CybersecurityFramework.deleted_at.is_(None)
        )
    ).first()
    
    if not framework:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Framework not found"
        )
    
    update_data = framework_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(framework, field, value)
    
    framework.updated_by = current_user.id
    framework.updated_at = datetime.utcnow()
    
    db.commit()
    db.refresh(framework)
    
    return framework


@router.delete("/frameworks/{framework_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_framework(
    framework_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Soft delete a cybersecurity framework"""
    
    framework = db.query(CybersecurityFramework).filter(
        and_(
            CybersecurityFramework.id == framework_id,
            CybersecurityFramework.deleted_at.is_(None)
        )
    ).first()
    
    if not framework:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Framework not found"
        )
    
    framework.deleted_at = datetime.utcnow()
    framework.updated_by = current_user.id
    
    db.commit()


# Domain Management Endpoints
@router.get("/frameworks/{framework_id}/domains", response_model=List[FrameworkDomainResponse])
async def list_framework_domains(
    framework_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """List all domains for a specific framework"""
    
    domains = db.query(FrameworkDomain).filter(
        and_(
            FrameworkDomain.framework_id == framework_id,
            FrameworkDomain.deleted_at.is_(None)
        )
    ).order_by(FrameworkDomain.sort_order, FrameworkDomain.name).all()
    
    return domains


@router.post("/frameworks/{framework_id}/domains", response_model=FrameworkDomainResponse, status_code=status.HTTP_201_CREATED)
async def create_framework_domain(
    framework_id: UUID,
    domain: FrameworkDomainCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new domain for a framework"""
    
    # Verify framework exists
    framework = db.query(CybersecurityFramework).filter(
        and_(
            CybersecurityFramework.id == framework_id,
            CybersecurityFramework.deleted_at.is_(None)
        )
    ).first()
    
    if not framework:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Framework not found"
        )
    
    # Check for duplicate domain_id within framework
    existing = db.query(FrameworkDomain).filter(
        and_(
            FrameworkDomain.framework_id == framework_id,
            FrameworkDomain.domain_id == domain.domain_id,
            FrameworkDomain.deleted_at.is_(None)
        )
    ).first()
    
    if existing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Domain {domain.domain_id} already exists in this framework"
        )
    
    db_domain = FrameworkDomain(
        **domain.dict(),
        framework_id=framework_id,
        created_by=current_user.id,
        updated_by=current_user.id
    )
    
    db.add(db_domain)
    db.commit()
    db.refresh(db_domain)
    
    return db_domain


# Control Management Endpoints
@router.get("/frameworks/{framework_id}/controls", response_model=List[FrameworkControlResponse])
async def list_framework_controls(
    framework_id: UUID,
    domain_id: Optional[str] = None,
    category_id: Optional[str] = None,
    control_type: Optional[str] = None,
    risk_rating: Optional[str] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """List all controls for a specific framework with optional filtering"""
    
    query = db.query(FrameworkControl).filter(
        and_(
            FrameworkControl.framework_id == framework_id,
            FrameworkControl.deleted_at.is_(None)
        )
    )
    
    if domain_id:
        query = query.join(FrameworkDomain).filter(FrameworkDomain.domain_id == domain_id)
    
    if category_id:
        query = query.join(FrameworkCategory).filter(FrameworkCategory.category_id == category_id)
    
    if control_type:
        query = query.filter(FrameworkControl.control_type == control_type)
    
    if risk_rating:
        query = query.filter(FrameworkControl.risk_rating == risk_rating)
    
    controls = query.order_by(
        FrameworkControl.sort_order,
        FrameworkControl.control_id
    ).offset(skip).limit(limit).all()
    
    return controls


@router.post("/frameworks/{framework_id}/controls", response_model=FrameworkControlResponse, status_code=status.HTTP_201_CREATED)
async def create_framework_control(
    framework_id: UUID,
    control: FrameworkControlCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new control for a framework"""
    
    # Verify framework exists
    framework = db.query(CybersecurityFramework).filter(
        and_(
            CybersecurityFramework.id == framework_id,
            CybersecurityFramework.deleted_at.is_(None)
        )
    ).first()
    
    if not framework:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Framework not found"
        )
    
    # Check for duplicate control_id within framework
    existing = db.query(FrameworkControl).filter(
        and_(
            FrameworkControl.framework_id == framework_id,
            FrameworkControl.control_id == control.control_id,
            FrameworkControl.deleted_at.is_(None)
        )
    ).first()
    
    if existing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Control {control.control_id} already exists in this framework"
        )
    
    db_control = FrameworkControl(
        **control.dict(),
        framework_id=framework_id,
        created_by=current_user.id,
        updated_by=current_user.id
    )
    
    db.add(db_control)
    db.commit()
    db.refresh(db_control)

    return db_control


# Implementation Management Endpoints
@router.get("/controls/{control_id}/implementations", response_model=List[ControlImplementationResponse])
async def list_control_implementations(
    control_id: UUID,
    organization_id: Optional[UUID] = None,
    status: Optional[ControlStatusEnum] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """List all implementations for a specific control"""

    query = db.query(ControlImplementation).filter(
        and_(
            ControlImplementation.control_id == control_id,
            ControlImplementation.deleted_at.is_(None)
        )
    )

    if organization_id:
        query = query.filter(ControlImplementation.organization_id == organization_id)

    if status:
        query = query.filter(ControlImplementation.status == status.value)

    implementations = query.order_by(ControlImplementation.created_at.desc()).all()
    return implementations


@router.post("/controls/{control_id}/implementations", response_model=ControlImplementationResponse, status_code=status.HTTP_201_CREATED)
async def create_control_implementation(
    control_id: UUID,
    implementation: ControlImplementationCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new implementation for a control"""

    # Verify control exists
    control = db.query(FrameworkControl).filter(
        and_(
            FrameworkControl.id == control_id,
            FrameworkControl.deleted_at.is_(None)
        )
    ).first()

    if not control:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Control not found"
        )

    db_implementation = ControlImplementation(
        **implementation.dict(),
        control_id=control_id,
        created_by=current_user.id,
        updated_by=current_user.id
    )

    db.add(db_implementation)
    db.commit()
    db.refresh(db_implementation)

    return db_implementation


@router.put("/implementations/{implementation_id}", response_model=ControlImplementationResponse)
async def update_control_implementation(
    implementation_id: UUID,
    implementation_update: ControlImplementationUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Update a control implementation"""

    implementation = db.query(ControlImplementation).filter(
        and_(
            ControlImplementation.id == implementation_id,
            ControlImplementation.deleted_at.is_(None)
        )
    ).first()

    if not implementation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Implementation not found"
        )

    update_data = implementation_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(implementation, field, value)

    implementation.updated_by = current_user.id
    implementation.updated_at = datetime.utcnow()

    db.commit()
    db.refresh(implementation)

    return implementation


# Assessment Management Endpoints
@router.get("/controls/{control_id}/assessments", response_model=List[ControlAssessmentResponse])
async def list_control_assessments(
    control_id: UUID,
    assessor: Optional[str] = None,
    assessment_type: Optional[str] = None,
    date_from: Optional[datetime] = None,
    date_to: Optional[datetime] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=500),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """List all assessments for a specific control"""

    query = db.query(ControlAssessment).filter(
        and_(
            ControlAssessment.control_id == control_id,
            ControlAssessment.deleted_at.is_(None)
        )
    )

    if assessor:
        query = query.filter(ControlAssessment.assessor.ilike(f"%{assessor}%"))

    if assessment_type:
        query = query.filter(ControlAssessment.assessment_type == assessment_type)

    if date_from:
        query = query.filter(ControlAssessment.assessment_date >= date_from)

    if date_to:
        query = query.filter(ControlAssessment.assessment_date <= date_to)

    assessments = query.order_by(
        ControlAssessment.assessment_date.desc()
    ).offset(skip).limit(limit).all()

    return assessments


@router.post("/controls/{control_id}/assessments", response_model=ControlAssessmentResponse, status_code=status.HTTP_201_CREATED)
async def create_control_assessment(
    control_id: UUID,
    assessment: ControlAssessmentCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new assessment for a control"""

    # Verify control exists
    control = db.query(FrameworkControl).filter(
        and_(
            FrameworkControl.id == control_id,
            FrameworkControl.deleted_at.is_(None)
        )
    ).first()

    if not control:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Control not found"
        )

    db_assessment = ControlAssessment(
        **assessment.dict(),
        control_id=control_id,
        created_by=current_user.id,
        updated_by=current_user.id
    )

    db.add(db_assessment)
    db.commit()
    db.refresh(db_assessment)

    return db_assessment


# Advanced Search Endpoint
@router.post("/search", response_model=FrameworkSearchResponse)
async def search_frameworks(
    search_request: FrameworkSearchRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Advanced search across frameworks, domains, categories, and controls"""

    # Base query for controls (most granular search)
    query = db.query(FrameworkControl).join(CybersecurityFramework).filter(
        and_(
            FrameworkControl.deleted_at.is_(None),
            CybersecurityFramework.deleted_at.is_(None)
        )
    )

    # Apply filters
    if search_request.query:
        search_term = f"%{search_request.query}%"
        query = query.filter(
            or_(
                FrameworkControl.name.ilike(search_term),
                FrameworkControl.description.ilike(search_term),
                FrameworkControl.control_id.ilike(search_term),
                CybersecurityFramework.name.ilike(search_term)
            )
        )

    if search_request.framework_types:
        framework_types = [ft.value for ft in search_request.framework_types]
        query = query.filter(CybersecurityFramework.framework_type.in_(framework_types))

    if search_request.control_types:
        query = query.filter(FrameworkControl.control_type.in_(search_request.control_types))

    if search_request.risk_levels:
        risk_levels = [rl.value for rl in search_request.risk_levels]
        query = query.filter(FrameworkControl.risk_rating.in_(risk_levels))

    if search_request.automation_levels:
        automation_levels = [al.value for al in search_request.automation_levels]
        query = query.filter(FrameworkControl.automation_level.in_(automation_levels))

    if search_request.tags:
        for tag in search_request.tags:
            query = query.filter(FrameworkControl.tags.contains([tag]))

    # Get total count
    total = query.count()

    # Apply sorting
    if search_request.sort_by == "name":
        sort_field = FrameworkControl.name
    elif search_request.sort_by == "control_id":
        sort_field = FrameworkControl.control_id
    elif search_request.sort_by == "risk_rating":
        sort_field = FrameworkControl.risk_rating
    else:
        sort_field = FrameworkControl.name

    if search_request.sort_order == "desc":
        sort_field = sort_field.desc()

    query = query.order_by(sort_field)

    # Apply pagination
    offset = (search_request.page - 1) * search_request.size
    results = query.offset(offset).limit(search_request.size).all()

    # Calculate pagination info
    pages = (total + search_request.size - 1) // search_request.size

    return FrameworkSearchResponse(
        total=total,
        page=search_request.page,
        size=search_request.size,
        pages=pages,
        results=results
    )


# Analytics Endpoints
@router.post("/analytics/gap-analysis", response_model=List[ComplianceGapAnalysis])
async def compliance_gap_analysis(
    analytics_request: FrameworkAnalyticsRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Perform compliance gap analysis across frameworks"""

    # Get frameworks to analyze
    framework_query = db.query(CybersecurityFramework).filter(
        CybersecurityFramework.deleted_at.is_(None)
    )

    if analytics_request.framework_ids:
        framework_query = framework_query.filter(
            CybersecurityFramework.id.in_(analytics_request.framework_ids)
        )

    frameworks = framework_query.all()
    gap_analyses = []

    for framework in frameworks:
        # Get all controls for this framework
        controls = db.query(FrameworkControl).filter(
            and_(
                FrameworkControl.framework_id == framework.id,
                FrameworkControl.deleted_at.is_(None)
            )
        ).all()

        total_controls = len(controls)
        if total_controls == 0:
            continue

        # Get implementation status for each control
        implemented_count = 0
        partially_implemented_count = 0
        not_implemented_count = 0
        critical_gaps = []
        high_priority_gaps = []

        for control in controls:
            # Get latest implementation for this control
            latest_implementation = db.query(ControlImplementation).filter(
                and_(
                    ControlImplementation.control_id == control.id,
                    ControlImplementation.deleted_at.is_(None)
                )
            ).order_by(ControlImplementation.updated_at.desc()).first()

            if latest_implementation:
                if latest_implementation.status in ["implemented", "verified"]:
                    implemented_count += 1
                elif latest_implementation.status == "partially_implemented":
                    partially_implemented_count += 1
                else:
                    not_implemented_count += 1

                    # Check for critical/high priority gaps
                    if control.risk_rating == "critical":
                        critical_gaps.append({
                            "control_id": control.control_id,
                            "name": control.name,
                            "risk_rating": control.risk_rating,
                            "status": latest_implementation.status,
                            "gaps": latest_implementation.gaps or []
                        })
                    elif control.risk_rating == "high":
                        high_priority_gaps.append({
                            "control_id": control.control_id,
                            "name": control.name,
                            "risk_rating": control.risk_rating,
                            "status": latest_implementation.status,
                            "gaps": latest_implementation.gaps or []
                        })
            else:
                not_implemented_count += 1

                # Add to gaps if high priority
                if control.risk_rating in ["critical", "high"]:
                    gap_item = {
                        "control_id": control.control_id,
                        "name": control.name,
                        "risk_rating": control.risk_rating,
                        "status": "not_implemented",
                        "gaps": ["No implementation found"]
                    }

                    if control.risk_rating == "critical":
                        critical_gaps.append(gap_item)
                    else:
                        high_priority_gaps.append(gap_item)

        # Calculate compliance percentage
        compliance_percentage = (implemented_count / total_controls) * 100 if total_controls > 0 else 0

        # Generate recommendations
        recommendations = []
        if compliance_percentage < 50:
            recommendations.append("Immediate action required - compliance below 50%")
        if len(critical_gaps) > 0:
            recommendations.append(f"Address {len(critical_gaps)} critical gaps immediately")
        if len(high_priority_gaps) > 5:
            recommendations.append("Focus on high-priority controls to improve compliance score")

        gap_analysis = ComplianceGapAnalysis(
            framework_id=framework.id,
            framework_name=framework.name,
            total_controls=total_controls,
            implemented_controls=implemented_count,
            partially_implemented_controls=partially_implemented_count,
            not_implemented_controls=not_implemented_count,
            compliance_percentage=compliance_percentage,
            critical_gaps=critical_gaps[:10],  # Limit to top 10
            high_priority_gaps=high_priority_gaps[:10],  # Limit to top 10
            recommendations=recommendations
        )

        gap_analyses.append(gap_analysis)

    return gap_analyses


@router.get("/analytics/dashboard", response_model=Dict[str, Any])
async def compliance_dashboard(
    framework_types: Optional[List[FrameworkTypeEnum]] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get compliance dashboard data with key metrics and trends"""

    # Base query for frameworks
    framework_query = db.query(CybersecurityFramework).filter(
        CybersecurityFramework.deleted_at.is_(None)
    )

    if framework_types:
        framework_type_values = [ft.value for ft in framework_types]
        framework_query = framework_query.filter(
            CybersecurityFramework.framework_type.in_(framework_type_values)
        )

    frameworks = framework_query.all()

    dashboard_data = {
        "summary": {
            "total_frameworks": len(frameworks),
            "total_controls": 0,
            "implemented_controls": 0,
            "average_compliance": 0.0
        },
        "framework_breakdown": [],
        "risk_distribution": {
            "critical": 0,
            "high": 0,
            "medium": 0,
            "low": 0
        },
        "implementation_trends": [],
        "top_gaps": [],
        "recent_assessments": []
    }

    total_controls_all = 0
    total_implemented_all = 0

    for framework in frameworks:
        # Get controls for this framework
        controls = db.query(FrameworkControl).filter(
            and_(
                FrameworkControl.framework_id == framework.id,
                FrameworkControl.deleted_at.is_(None)
            )
        ).all()

        framework_total = len(controls)
        framework_implemented = 0

        # Count risk levels
        for control in controls:
            if control.risk_rating:
                dashboard_data["risk_distribution"][control.risk_rating] += 1

            # Check implementation status
            latest_impl = db.query(ControlImplementation).filter(
                and_(
                    ControlImplementation.control_id == control.id,
                    ControlImplementation.deleted_at.is_(None)
                )
            ).order_by(ControlImplementation.updated_at.desc()).first()

            if latest_impl and latest_impl.status in ["implemented", "verified"]:
                framework_implemented += 1

        framework_compliance = (framework_implemented / framework_total * 100) if framework_total > 0 else 0

        dashboard_data["framework_breakdown"].append({
            "framework_name": framework.name,
            "framework_type": framework.framework_type,
            "total_controls": framework_total,
            "implemented_controls": framework_implemented,
            "compliance_percentage": framework_compliance
        })

        total_controls_all += framework_total
        total_implemented_all += framework_implemented

    # Update summary
    dashboard_data["summary"]["total_controls"] = total_controls_all
    dashboard_data["summary"]["implemented_controls"] = total_implemented_all
    dashboard_data["summary"]["average_compliance"] = (
        (total_implemented_all / total_controls_all * 100) if total_controls_all > 0 else 0
    )

    # Get recent assessments
    recent_assessments = db.query(ControlAssessment).filter(
        ControlAssessment.deleted_at.is_(None)
    ).order_by(ControlAssessment.assessment_date.desc()).limit(10).all()

    dashboard_data["recent_assessments"] = [
        {
            "assessment_id": str(assessment.id),
            "control_id": assessment.control.control_id if assessment.control else "Unknown",
            "assessor": assessment.assessor,
            "assessment_date": assessment.assessment_date.isoformat(),
            "status": assessment.status,
            "compliance_score": assessment.compliance_score
        }
        for assessment in recent_assessments
    ]

    return dashboard_data


# Framework Mapping Endpoints
@router.get("/mapping/cross-framework", response_model=Dict[str, Any])
async def cross_framework_mapping(
    source_framework_id: UUID,
    target_framework_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get cross-framework control mappings between two frameworks"""

    # Verify both frameworks exist
    source_framework = db.query(CybersecurityFramework).filter(
        and_(
            CybersecurityFramework.id == source_framework_id,
            CybersecurityFramework.deleted_at.is_(None)
        )
    ).first()

    target_framework = db.query(CybersecurityFramework).filter(
        and_(
            CybersecurityFramework.id == target_framework_id,
            CybersecurityFramework.deleted_at.is_(None)
        )
    ).first()

    if not source_framework or not target_framework:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="One or both frameworks not found"
        )

    # Get controls from both frameworks
    source_controls = db.query(FrameworkControl).filter(
        and_(
            FrameworkControl.framework_id == source_framework_id,
            FrameworkControl.deleted_at.is_(None)
        )
    ).all()

    target_controls = db.query(FrameworkControl).filter(
        and_(
            FrameworkControl.framework_id == target_framework_id,
            FrameworkControl.deleted_at.is_(None)
        )
    ).all()

    # Simple mapping based on similar objectives/descriptions
    # In a real implementation, this would use a more sophisticated mapping algorithm
    mappings = []

    for source_control in source_controls:
        potential_matches = []

        for target_control in target_controls:
            # Simple similarity check based on keywords in objectives/descriptions
            similarity_score = _calculate_control_similarity(source_control, target_control)

            if similarity_score > 0.3:  # Threshold for potential match
                potential_matches.append({
                    "target_control_id": target_control.control_id,
                    "target_control_name": target_control.name,
                    "similarity_score": similarity_score,
                    "mapping_rationale": f"Similar objectives and control types"
                })

        # Sort by similarity score
        potential_matches.sort(key=lambda x: x["similarity_score"], reverse=True)

        mappings.append({
            "source_control_id": source_control.control_id,
            "source_control_name": source_control.name,
            "potential_matches": potential_matches[:3]  # Top 3 matches
        })

    return {
        "source_framework": {
            "id": str(source_framework.id),
            "name": source_framework.name,
            "type": source_framework.framework_type
        },
        "target_framework": {
            "id": str(target_framework.id),
            "name": target_framework.name,
            "type": target_framework.framework_type
        },
        "mappings": mappings,
        "mapping_metadata": {
            "generated_at": datetime.utcnow().isoformat(),
            "total_source_controls": len(source_controls),
            "total_target_controls": len(target_controls),
            "mapping_algorithm": "keyword_similarity_v1"
        }
    }


def _calculate_control_similarity(control1: FrameworkControl, control2: FrameworkControl) -> float:
    """Calculate similarity score between two controls"""

    # Simple keyword-based similarity
    # In production, this would use more sophisticated NLP techniques

    text1 = f"{control1.name} {control1.description or ''} {control1.objective or ''}".lower()
    text2 = f"{control2.name} {control2.description or ''} {control2.objective or ''}".lower()

    # Common security keywords
    keywords = [
        "access", "authentication", "authorization", "encryption", "monitoring",
        "logging", "backup", "recovery", "incident", "vulnerability", "patch",
        "configuration", "network", "firewall", "antivirus", "malware"
    ]

    matches = 0
    total_keywords = len(keywords)

    for keyword in keywords:
        if keyword in text1 and keyword in text2:
            matches += 1

    # Also check control type similarity
    type_bonus = 0.2 if control1.control_type == control2.control_type else 0

    return (matches / total_keywords) + type_bonus
