"""
Real-time Monitoring API Endpoints

WebSocket and HTTP endpoints for real-time monitoring dashboard functionality.
"""

import asyncio
import json
import logging
from typing import Dict, Any, List
from datetime import datetime

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from app.api.security import get_current_user
from app.db.session import get_db
from app.models.user import User
from app.services.realtime_monitoring_service import (
    realtime_monitoring_service,
    ThreatMapData,
    RiskHeatmapData,
    AttackTimelineEvent
)
from app.services.compliance_monitoring_service import ComplianceMonitoringService
from app.core.config import settings


logger = logging.getLogger(__name__)

router = APIRouter()


@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket, token: str = None):
    """
    WebSocket endpoint for real-time monitoring updates
    
    Provides real-time streaming of:
    - Threat detection events
    - Attack path discoveries
    - MITRE ATT&CK correlations
    - Risk score changes
    - System alerts
    """
    await websocket.accept()
    
    try:
        # TODO: Add WebSocket authentication
        # For now, we'll accept all connections
        # In production, validate the token parameter
        
        await realtime_monitoring_service.add_connection(websocket)
        
        # Send initial dashboard data
        db = next(get_db())
        try:
            initial_data = await realtime_monitoring_service.get_dashboard_overview(db)
            await websocket.send_text(json.dumps({
                "type": "initial_data",
                "data": initial_data
            }))
        finally:
            db.close()
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for client messages (ping/pong, requests, etc.)
                message = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                
                # Handle client requests
                try:
                    request = json.loads(message)
                    await handle_websocket_request(websocket, request)
                except json.JSONDecodeError:
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "message": "Invalid JSON format"
                    }))
                    
            except asyncio.TimeoutError:
                # Send ping to keep connection alive
                await websocket.send_text(json.dumps({
                    "type": "ping",
                    "timestamp": datetime.utcnow().isoformat()
                }))
                
    except WebSocketDisconnect:
        logger.info("WebSocket client disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
    finally:
        await realtime_monitoring_service.remove_connection(websocket)


async def handle_websocket_request(websocket: WebSocket, request: Dict[str, Any]):
    """Handle incoming WebSocket requests from clients"""
    
    request_type = request.get("type")
    
    if request_type == "get_dashboard_overview":
        db = next(get_db())
        try:
            data = await realtime_monitoring_service.get_dashboard_overview(db)
            await websocket.send_text(json.dumps({
                "type": "dashboard_overview",
                "data": data
            }))
        finally:
            db.close()
            
    elif request_type == "get_threat_map":
        db = next(get_db())
        try:
            data = await realtime_monitoring_service.get_threat_map_data(db)
            await websocket.send_text(json.dumps({
                "type": "threat_map",
                "data": [asdict(item) for item in data]
            }))
        finally:
            db.close()
            
    elif request_type == "get_attack_timeline":
        hours = request.get("hours", 24)
        db = next(get_db())
        try:
            data = await realtime_monitoring_service.get_attack_timeline(db, hours)
            await websocket.send_text(json.dumps({
                "type": "attack_timeline",
                "data": [asdict(item) for item in data]
            }))
        finally:
            db.close()

    elif request_type == "get_compliance_data":
        db = next(get_db())
        try:
            compliance_service = ComplianceMonitoringService(db)
            data = await compliance_service.get_real_time_compliance_data()
            await websocket.send_text(json.dumps({
                "type": "compliance_data",
                "data": data
            }))
        finally:
            db.close()

    elif request_type == "pong":
        # Client responded to ping
        pass
        
    else:
        await websocket.send_text(json.dumps({
            "type": "error",
            "message": f"Unknown request type: {request_type}"
        }))


@router.get("/dashboard/overview")
async def get_dashboard_overview(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get comprehensive dashboard overview data
    
    Returns:
    - Asset statistics
    - Recent security events
    - MITRE technique distribution
    - Threat trends
    """
    try:
        data = await realtime_monitoring_service.get_dashboard_overview(db)
        return {"success": True, "data": data}
    except Exception as e:
        logger.error(f"Error getting dashboard overview: {e}")
        raise HTTPException(status_code=500, detail="Failed to get dashboard overview")


@router.get("/threat-map")
async def get_threat_map(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get geographic threat distribution data for threat map visualization
    
    Returns threat data by country including:
    - Threat counts
    - Severity distribution
    - Top MITRE techniques
    - Geographic coordinates
    """
    try:
        data = await realtime_monitoring_service.get_threat_map_data(db)
        return {
            "success": True,
            "data": [
                {
                    "country": item.country,
                    "country_code": item.country_code,
                    "threat_count": item.threat_count,
                    "severity_distribution": item.severity_distribution,
                    "top_techniques": item.top_techniques,
                    "coordinates": item.coordinates
                }
                for item in data
            ]
        }
    except Exception as e:
        logger.error(f"Error getting threat map data: {e}")
        raise HTTPException(status_code=500, detail="Failed to get threat map data")


@router.get("/risk-heatmap")
async def get_risk_heatmap(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get risk heatmap data for asset visualization
    
    Returns risk data for assets including:
    - Risk scores
    - Threat counts
    - Associated MITRE techniques
    - Last update timestamps
    """
    try:
        data = await realtime_monitoring_service.get_risk_heatmap_data(db)
        return {
            "success": True,
            "data": [
                {
                    "asset_id": item.asset_id,
                    "asset_name": item.asset_name,
                    "asset_type": item.asset_type,
                    "risk_score": item.risk_score,
                    "threat_count": item.threat_count,
                    "mitre_techniques": item.mitre_techniques,
                    "last_updated": item.last_updated.isoformat()
                }
                for item in data
            ]
        }
    except Exception as e:
        logger.error(f"Error getting risk heatmap data: {e}")
        raise HTTPException(status_code=500, detail="Failed to get risk heatmap data")


@router.get("/attack-timeline")
async def get_attack_timeline(
    hours: int = 24,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get attack timeline events for specified time period
    
    Args:
        hours: Number of hours to look back (default: 24)
    
    Returns chronological list of attack events with:
    - MITRE technique information
    - Asset details
    - Severity levels
    - Event descriptions
    """
    try:
        if hours < 1 or hours > 168:  # Max 1 week
            raise HTTPException(status_code=400, detail="Hours must be between 1 and 168")
            
        data = await realtime_monitoring_service.get_attack_timeline(db, hours)
        return {
            "success": True,
            "data": [
                {
                    "timestamp": item.timestamp.isoformat(),
                    "event_id": item.event_id,
                    "technique_id": item.technique_id,
                    "technique_name": item.technique_name,
                    "tactic": item.tactic,
                    "asset_name": item.asset_name,
                    "severity": item.severity.value,
                    "description": item.description
                }
                for item in data
            ]
        }
    except Exception as e:
        logger.error(f"Error getting attack timeline: {e}")
        raise HTTPException(status_code=500, detail="Failed to get attack timeline")


@router.get("/mitre-visualization")
async def get_mitre_visualization(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get MITRE ATT&CK visualization data
    
    Returns:
    - MITRE matrix with technique usage
    - Technique statistics
    - Top active techniques
    - Risk levels by technique
    """
    try:
        data = await realtime_monitoring_service.get_mitre_visualization_data(db)
        return {"success": True, "data": data}
    except Exception as e:
        logger.error(f"Error getting MITRE visualization data: {e}")
        raise HTTPException(status_code=500, detail="Failed to get MITRE visualization data")


@router.post("/simulate-events")
async def simulate_events(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Simulate real-time events for demonstration purposes
    
    This endpoint is for development and demo purposes only.
    In production, real events would be generated by the monitoring system.
    """
    if not settings.DEBUG:
        raise HTTPException(status_code=403, detail="Event simulation only available in debug mode")
    
    try:
        background_tasks.add_task(realtime_monitoring_service.simulate_real_time_events, db)
        return {
            "success": True,
            "message": "Event simulation started"
        }
    except Exception as e:
        logger.error(f"Error starting event simulation: {e}")
        raise HTTPException(status_code=500, detail="Failed to start event simulation")


@router.get("/compliance/dashboard")
async def get_compliance_dashboard(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get real-time compliance dashboard data

    Returns:
    - Framework compliance summary
    - Recent assessments
    - Critical gaps
    - Implementation status
    """
    try:
        compliance_service = ComplianceMonitoringService(db)
        data = await compliance_service.get_real_time_compliance_data()
        return {"success": True, "data": data}
    except Exception as e:
        logger.error(f"Error getting compliance dashboard: {e}")
        raise HTTPException(status_code=500, detail="Failed to get compliance dashboard")


@router.post("/compliance/start-monitoring")
async def start_compliance_monitoring(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Start real-time compliance monitoring for the current user
    """
    try:
        compliance_service = ComplianceMonitoringService(db)
        background_tasks.add_task(
            compliance_service.start_real_time_monitoring,
            current_user.id
        )
        return {
            "success": True,
            "message": "Compliance monitoring started",
            "user_id": str(current_user.id)
        }
    except Exception as e:
        logger.error(f"Error starting compliance monitoring: {e}")
        raise HTTPException(status_code=500, detail="Failed to start compliance monitoring")


@router.get("/metrics")
async def get_monitoring_metrics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get real-time monitoring system metrics

    Returns:
    - Active WebSocket connections
    - Event buffer statistics
    - System performance metrics
    """
    try:
        metrics = {
            "active_connections": len(realtime_monitoring_service.active_connections),
            "event_buffer_size": len(realtime_monitoring_service.event_buffer),
            "max_buffer_size": realtime_monitoring_service.max_buffer_size,
            "system_status": "healthy",
            "last_updated": datetime.utcnow().isoformat()
        }

        return {"success": True, "data": metrics}
    except Exception as e:
        logger.error(f"Error getting monitoring metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get monitoring metrics")


# Background task to periodically simulate events in development
async def start_event_simulation_loop():
    """Start background event simulation for development"""
    if not settings.DEBUG:
        return
        
    while True:
        try:
            db = next(get_db())
            try:
                await realtime_monitoring_service.simulate_real_time_events(db)
            finally:
                db.close()
            await asyncio.sleep(30)  # Simulate events every 30 seconds
        except Exception as e:
            logger.error(f"Error in event simulation loop: {e}")
            await asyncio.sleep(60)  # Wait longer on error


# Helper function to convert dataclass to dict
def asdict(obj):
    """Convert dataclass to dictionary"""
    if hasattr(obj, '__dict__'):
        result = {}
        for key, value in obj.__dict__.items():
            if isinstance(value, datetime):
                result[key] = value.isoformat()
            elif hasattr(value, 'value'):  # Enum
                result[key] = value.value
            else:
                result[key] = value
        return result
    return obj
