"""Pydantic schemas for API request/response models."""

from .auth import (
    LoginRequest,
    LoginResponse,
    MFABackupCodesResponse,
    MFASetupRequest,
    MFAVerifyRequest,
    PasswordResetConfirm,
    PasswordResetRequest,
    RefreshTokenRequest,
    TokenResponse,
)
from .user import (
    UserAPIKeyCreate,
    UserAPIKeyResponse,
    UserCreate,
    UserListResponse,
    UserResponse,
    UserRoleAssignment,
    UserSessionResponse,
    UserUpdate,
)
from .asset import (
    AssetCreate,
    AssetUpdate,
    AssetResponse,
    AssetListResponse,
    AssetSearchRequest,
    AssetRelationshipCreate,
    AssetRelationshipUpdate,
    AssetRelationshipResponse,
    DiscoveryJobCreate,
    DiscoveryJobUpdate,
    DiscoveryJobResponse,
    DiscoveryJobListResponse,
    AssetTagCreate,
    AssetTagUpdate,
    AssetTagResponse,
    AssetStatistics,
    AssetHealthCheck,
)
from .mitre import (
    MitreTechniqueBase,
    MitreTechniqueCreate,
    MitreTechniqueUpdate,
    MitreTechniqueResponse,
    MitreTechniqueListResponse,
    MitreTacticBase,
    MitreTacticCreate,
    MitreTacticUpdate,
    MitreTacticResponse,
    MitreTacticListResponse,
    MitreGroupBase,
    MitreGroupCreate,
    MitreGroupUpdate,
    MitreGroupResponse,
    MitreGroupListResponse,
    MitreSoftwareBase,
    MitreSoftwareCreate,
    MitreSoftwareUpdate,
    MitreSoftwareResponse,
    MitreMitigationBase,
    MitreMitigationCreate,
    MitreMitigationUpdate,
    MitreMitigationResponse,
    MitreDataSyncBase,
    MitreDataSyncCreate,
    MitreDataSyncUpdate,
    MitreDataSyncResponse,
    MitreSearchRequest,
    MitreSearchResponse,
)
from .cybersecurity_frameworks import (
    # Framework schemas
    CybersecurityFrameworkCreate,
    CybersecurityFrameworkUpdate,
    CybersecurityFrameworkResponse,
    # Domain schemas
    FrameworkDomainCreate,
    FrameworkDomainUpdate,
    FrameworkDomainResponse,
    # Category schemas
    FrameworkCategoryCreate,
    FrameworkCategoryUpdate,
    FrameworkCategoryResponse,
    # Control schemas
    FrameworkControlCreate,
    FrameworkControlUpdate,
    FrameworkControlResponse,
    # Implementation schemas
    ControlImplementationCreate,
    ControlImplementationUpdate,
    ControlImplementationResponse,
    # Assessment schemas
    ControlAssessmentCreate,
    ControlAssessmentUpdate,
    ControlAssessmentResponse,
    # Search and analytics
    FrameworkSearchRequest,
    FrameworkSearchResponse,
    FrameworkAnalyticsRequest,
    FrameworkAnalyticsResponse,
    ComplianceGapAnalysis,
    # Enums
    FrameworkTypeEnum,
    ControlStatusEnum,
    RiskLevelEnum,
    AutomationLevelEnum,
)

__all__ = [
    # Authentication schemas
    "LoginRequest",
    "LoginResponse",
    "TokenResponse",
    "RefreshTokenRequest",
    "PasswordResetRequest",
    "PasswordResetConfirm",
    "MFASetupRequest",
    "MFAVerifyRequest",
    "MFABackupCodesResponse",
    # User schemas
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "UserListResponse",
    "UserRoleAssignment",
    "UserSessionResponse",
    "UserAPIKeyCreate",
    "UserAPIKeyResponse",
    # Asset schemas
    "AssetCreate",
    "AssetUpdate",
    "AssetResponse",
    "AssetListResponse",
    "AssetSearchRequest",
    "AssetRelationshipCreate",
    "AssetRelationshipUpdate",
    "AssetRelationshipResponse",
    "DiscoveryJobCreate",
    "DiscoveryJobUpdate",
    "DiscoveryJobResponse",
    "DiscoveryJobListResponse",
    "AssetTagCreate",
    "AssetTagUpdate",
    "AssetTagResponse",
    "AssetStatistics",
    "AssetHealthCheck",
    # MITRE schemas
    "MitreTechniqueBase",
    "MitreTechniqueCreate",
    "MitreTechniqueUpdate",
    "MitreTechniqueResponse",
    "MitreTechniqueListResponse",
    "MitreTacticBase",
    "MitreTacticCreate",
    "MitreTacticUpdate",
    "MitreTacticResponse",
    "MitreTacticListResponse",
    "MitreGroupBase",
    "MitreGroupCreate",
    "MitreGroupUpdate",
    "MitreGroupResponse",
    "MitreGroupListResponse",
    "MitreSoftwareBase",
    "MitreSoftwareCreate",
    "MitreSoftwareUpdate",
    "MitreSoftwareResponse",
    "MitreMitigationBase",
    "MitreMitigationCreate",
    "MitreMitigationUpdate",
    "MitreMitigationResponse",
    "MitreDataSyncBase",
    "MitreDataSyncCreate",
    "MitreDataSyncUpdate",
    "MitreDataSyncResponse",
    "MitreSearchRequest",
    "MitreSearchResponse",
]
