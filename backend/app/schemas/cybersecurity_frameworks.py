"""
Pydantic schemas for Cybersecurity Frameworks API
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from uuid import UUID
from enum import Enum

from pydantic import BaseModel, Field, validator


class FrameworkTypeEnum(str, Enum):
    """Supported cybersecurity frameworks"""
    ISF_2022 = "isf_2022"
    NIST_CSF_2_0 = "nist_csf_2_0"
    ISO_27001_2022 = "iso_27001_2022"
    CIS_CONTROLS_V8 = "cis_controls_v8"


class ControlStatusEnum(str, Enum):
    """Control implementation status"""
    NOT_IMPLEMENTED = "not_implemented"
    PARTIALLY_IMPLEMENTED = "partially_implemented"
    IMPLEMENTED = "implemented"
    VERIFIED = "verified"
    NON_COMPLIANT = "non_compliant"


class RiskLevelEnum(str, Enum):
    """Risk level classification"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AutomationLevelEnum(str, Enum):
    """Control automation level"""
    MANUAL = "manual"
    SEMI_AUTOMATED = "semi_automated"
    AUTOMATED = "automated"


# Base schemas
class CybersecurityFrameworkBase(BaseModel):
    """Base schema for cybersecurity frameworks"""
    name: str = Field(..., max_length=255, description="Framework name")
    framework_type: FrameworkTypeEnum = Field(..., description="Framework type")
    version: str = Field(..., max_length=50, description="Framework version")
    description: Optional[str] = Field(None, description="Framework description")
    authority: Optional[str] = Field(None, max_length=255, description="Issuing authority")
    publication_date: Optional[datetime] = Field(None, description="Publication date")
    effective_date: Optional[datetime] = Field(None, description="Effective date")
    status: str = Field(default="active", description="Framework status")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class CybersecurityFrameworkCreate(CybersecurityFrameworkBase):
    """Schema for creating a cybersecurity framework"""
    pass


class CybersecurityFrameworkUpdate(BaseModel):
    """Schema for updating a cybersecurity framework"""
    name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    authority: Optional[str] = Field(None, max_length=255)
    publication_date: Optional[datetime] = None
    effective_date: Optional[datetime] = None
    status: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class CybersecurityFrameworkResponse(CybersecurityFrameworkBase):
    """Schema for cybersecurity framework response"""
    id: UUID
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Domain schemas
class FrameworkDomainBase(BaseModel):
    """Base schema for framework domains"""
    domain_id: str = Field(..., max_length=100, description="Domain identifier")
    name: str = Field(..., max_length=500, description="Domain name")
    description: Optional[str] = Field(None, description="Domain description")
    sort_order: int = Field(default=0, description="Sort order")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class FrameworkDomainCreate(FrameworkDomainBase):
    """Schema for creating a framework domain"""
    framework_id: UUID = Field(..., description="Framework ID")


class FrameworkDomainUpdate(BaseModel):
    """Schema for updating a framework domain"""
    name: Optional[str] = Field(None, max_length=500)
    description: Optional[str] = None
    sort_order: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None


class FrameworkDomainResponse(FrameworkDomainBase):
    """Schema for framework domain response"""
    id: UUID
    framework_id: UUID
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Category schemas
class FrameworkCategoryBase(BaseModel):
    """Base schema for framework categories"""
    category_id: str = Field(..., max_length=100, description="Category identifier")
    name: str = Field(..., max_length=500, description="Category name")
    description: Optional[str] = Field(None, description="Category description")
    sort_order: int = Field(default=0, description="Sort order")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class FrameworkCategoryCreate(FrameworkCategoryBase):
    """Schema for creating a framework category"""
    domain_id: UUID = Field(..., description="Domain ID")
    parent_category_id: Optional[UUID] = Field(None, description="Parent category ID")


class FrameworkCategoryUpdate(BaseModel):
    """Schema for updating a framework category"""
    name: Optional[str] = Field(None, max_length=500)
    description: Optional[str] = None
    sort_order: Optional[int] = None
    parent_category_id: Optional[UUID] = None
    metadata: Optional[Dict[str, Any]] = None


class FrameworkCategoryResponse(FrameworkCategoryBase):
    """Schema for framework category response"""
    id: UUID
    domain_id: UUID
    parent_category_id: Optional[UUID]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Control schemas
class FrameworkControlBase(BaseModel):
    """Base schema for framework controls"""
    control_id: str = Field(..., max_length=100, description="Control identifier")
    name: str = Field(..., max_length=500, description="Control name")
    description: Optional[str] = Field(None, description="Control description")
    objective: Optional[str] = Field(None, description="Control objective")
    implementation_guidance: Optional[str] = Field(None, description="Implementation guidance")
    control_type: Optional[str] = Field(None, max_length=50, description="Control type")
    automation_level: Optional[AutomationLevelEnum] = Field(None, description="Automation level")
    risk_rating: Optional[RiskLevelEnum] = Field(None, description="Risk rating")
    priority: Optional[str] = Field(None, max_length=50, description="Priority level")
    assessment_criteria: Optional[Dict[str, Any]] = Field(None, description="Assessment criteria")
    testing_procedures: Optional[Dict[str, Any]] = Field(None, description="Testing procedures")
    evidence_requirements: Optional[Dict[str, Any]] = Field(None, description="Evidence requirements")
    sort_order: int = Field(default=0, description="Sort order")
    references: Optional[Dict[str, Any]] = Field(None, description="External references")
    tags: Optional[List[str]] = Field(None, description="Searchable tags")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class FrameworkControlCreate(FrameworkControlBase):
    """Schema for creating a framework control"""
    framework_id: UUID = Field(..., description="Framework ID")
    domain_id: Optional[UUID] = Field(None, description="Domain ID")
    category_id: Optional[UUID] = Field(None, description="Category ID")
    parent_control_id: Optional[UUID] = Field(None, description="Parent control ID")


class FrameworkControlUpdate(BaseModel):
    """Schema for updating a framework control"""
    name: Optional[str] = Field(None, max_length=500)
    description: Optional[str] = None
    objective: Optional[str] = None
    implementation_guidance: Optional[str] = None
    control_type: Optional[str] = Field(None, max_length=50)
    automation_level: Optional[AutomationLevelEnum] = None
    risk_rating: Optional[RiskLevelEnum] = None
    priority: Optional[str] = Field(None, max_length=50)
    assessment_criteria: Optional[Dict[str, Any]] = None
    testing_procedures: Optional[Dict[str, Any]] = None
    evidence_requirements: Optional[Dict[str, Any]] = None
    sort_order: Optional[int] = None
    references: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


class FrameworkControlResponse(FrameworkControlBase):
    """Schema for framework control response"""
    id: UUID
    framework_id: UUID
    domain_id: Optional[UUID]
    category_id: Optional[UUID]
    parent_control_id: Optional[UUID]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Implementation schemas
class ControlImplementationBase(BaseModel):
    """Base schema for control implementations"""
    status: ControlStatusEnum = Field(default=ControlStatusEnum.NOT_IMPLEMENTED, description="Implementation status")
    implementation_date: Optional[datetime] = Field(None, description="Implementation date")
    target_completion_date: Optional[datetime] = Field(None, description="Target completion date")
    responsible_party: Optional[str] = Field(None, max_length=255, description="Responsible party")
    evidence: Optional[List[Dict[str, Any]]] = Field(None, description="Implementation evidence")
    documentation: Optional[List[Dict[str, Any]]] = Field(None, description="Documentation links")
    automated_checks: Optional[List[Dict[str, Any]]] = Field(None, description="Automated validation rules")
    gaps: Optional[List[Dict[str, Any]]] = Field(None, description="Identified gaps")
    remediation_plan: Optional[str] = Field(None, description="Remediation plan")
    remediation_priority: Optional[RiskLevelEnum] = Field(None, description="Remediation priority")
    implementation_cost: Optional[float] = Field(None, ge=0, description="Implementation cost")
    effort_estimate: Optional[float] = Field(None, ge=0, description="Effort estimate in hours")


class ControlImplementationCreate(ControlImplementationBase):
    """Schema for creating a control implementation"""
    control_id: UUID = Field(..., description="Control ID")
    organization_id: Optional[UUID] = Field(None, description="Organization ID")


class ControlImplementationUpdate(BaseModel):
    """Schema for updating a control implementation"""
    status: Optional[ControlStatusEnum] = None
    implementation_date: Optional[datetime] = None
    target_completion_date: Optional[datetime] = None
    responsible_party: Optional[str] = Field(None, max_length=255)
    evidence: Optional[List[Dict[str, Any]]] = None
    documentation: Optional[List[Dict[str, Any]]] = None
    automated_checks: Optional[List[Dict[str, Any]]] = None
    gaps: Optional[List[Dict[str, Any]]] = None
    remediation_plan: Optional[str] = None
    remediation_priority: Optional[RiskLevelEnum] = None
    implementation_cost: Optional[float] = Field(None, ge=0)
    effort_estimate: Optional[float] = Field(None, ge=0)


class ControlImplementationResponse(ControlImplementationBase):
    """Schema for control implementation response"""
    id: UUID
    control_id: UUID
    organization_id: Optional[UUID]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Assessment schemas
class ControlAssessmentBase(BaseModel):
    """Base schema for control assessments"""
    assessment_date: datetime = Field(default_factory=datetime.utcnow, description="Assessment date")
    assessor: str = Field(..., max_length=255, description="Assessor name")
    assessment_type: Optional[str] = Field(None, max_length=50, description="Assessment type")
    status: ControlStatusEnum = Field(..., description="Assessment status")
    effectiveness_rating: Optional[str] = Field(None, max_length=50, description="Effectiveness rating")
    maturity_level: Optional[int] = Field(None, ge=1, le=5, description="Maturity level (1-5)")
    confidence_level: Optional[str] = Field(None, max_length=50, description="Confidence level")
    findings: Optional[List[Dict[str, Any]]] = Field(None, description="Assessment findings")
    recommendations: Optional[List[Dict[str, Any]]] = Field(None, description="Recommendations")
    evidence_reviewed: Optional[List[Dict[str, Any]]] = Field(None, description="Evidence reviewed")
    compliance_score: Optional[float] = Field(None, ge=0, le=100, description="Compliance score (0-100)")
    risk_score: Optional[float] = Field(None, ge=0, description="Risk score")
    next_assessment_date: Optional[datetime] = Field(None, description="Next assessment date")
    assessment_frequency: Optional[str] = Field(None, max_length=50, description="Assessment frequency")


class ControlAssessmentCreate(ControlAssessmentBase):
    """Schema for creating a control assessment"""
    control_id: UUID = Field(..., description="Control ID")
    implementation_id: Optional[UUID] = Field(None, description="Implementation ID")


class ControlAssessmentUpdate(BaseModel):
    """Schema for updating a control assessment"""
    assessor: Optional[str] = Field(None, max_length=255)
    assessment_type: Optional[str] = Field(None, max_length=50)
    status: Optional[ControlStatusEnum] = None
    effectiveness_rating: Optional[str] = Field(None, max_length=50)
    maturity_level: Optional[int] = Field(None, ge=1, le=5)
    confidence_level: Optional[str] = Field(None, max_length=50)
    findings: Optional[List[Dict[str, Any]]] = None
    recommendations: Optional[List[Dict[str, Any]]] = None
    evidence_reviewed: Optional[List[Dict[str, Any]]] = None
    compliance_score: Optional[float] = Field(None, ge=0, le=100)
    risk_score: Optional[float] = Field(None, ge=0)
    next_assessment_date: Optional[datetime] = None
    assessment_frequency: Optional[str] = Field(None, max_length=50)


class ControlAssessmentResponse(ControlAssessmentBase):
    """Schema for control assessment response"""
    id: UUID
    control_id: UUID
    implementation_id: Optional[UUID]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Search and filtering schemas
class FrameworkSearchRequest(BaseModel):
    """Schema for framework search requests"""
    query: Optional[str] = Field(None, description="Search query")
    framework_types: Optional[List[FrameworkTypeEnum]] = Field(None, description="Framework types to search")
    domains: Optional[List[str]] = Field(None, description="Domain IDs to filter")
    categories: Optional[List[str]] = Field(None, description="Category IDs to filter")
    control_types: Optional[List[str]] = Field(None, description="Control types to filter")
    risk_levels: Optional[List[RiskLevelEnum]] = Field(None, description="Risk levels to filter")
    automation_levels: Optional[List[AutomationLevelEnum]] = Field(None, description="Automation levels to filter")
    implementation_status: Optional[List[ControlStatusEnum]] = Field(None, description="Implementation status to filter")
    tags: Optional[List[str]] = Field(None, description="Tags to filter")
    page: int = Field(default=1, ge=1, description="Page number")
    size: int = Field(default=20, ge=1, le=100, description="Page size")
    sort_by: Optional[str] = Field(default="name", description="Sort field")
    sort_order: Optional[str] = Field(default="asc", pattern="^(asc|desc)$", description="Sort order")


class FrameworkSearchResponse(BaseModel):
    """Schema for framework search responses"""
    total: int = Field(..., description="Total number of results")
    page: int = Field(..., description="Current page")
    size: int = Field(..., description="Page size")
    pages: int = Field(..., description="Total pages")
    results: List[Union[CybersecurityFrameworkResponse, FrameworkControlResponse]] = Field(..., description="Search results")


# Analytics schemas
class FrameworkAnalyticsRequest(BaseModel):
    """Schema for framework analytics requests"""
    framework_ids: Optional[List[UUID]] = Field(None, description="Framework IDs to analyze")
    date_from: Optional[datetime] = Field(None, description="Start date for analysis")
    date_to: Optional[datetime] = Field(None, description="End date for analysis")
    metrics: Optional[List[str]] = Field(None, description="Specific metrics to include")


class ComplianceGapAnalysis(BaseModel):
    """Schema for compliance gap analysis"""
    framework_id: UUID
    framework_name: str
    total_controls: int
    implemented_controls: int
    partially_implemented_controls: int
    not_implemented_controls: int
    compliance_percentage: float
    critical_gaps: List[Dict[str, Any]]
    high_priority_gaps: List[Dict[str, Any]]
    recommendations: List[str]


class FrameworkAnalyticsResponse(BaseModel):
    """Schema for framework analytics responses"""
    generated_at: datetime = Field(default_factory=datetime.utcnow)
    summary: Dict[str, Any]
    gap_analysis: List[ComplianceGapAnalysis]
    trends: Dict[str, Any]
    recommendations: List[str]
