"""
Sample data for cybersecurity frameworks
Contains structured data for ISF 2022, NIST CSF 2.0, ISO/IEC 27001:2022, and CIS Controls v8
"""

from datetime import datetime
from typing import Dict, Any

# ISF 2022 Sample Data
ISF_2022_SAMPLE = {
    "name": "Information Security Forum Standard of Good Practice 2022",
    "version": "2022",
    "framework_type": "isf_2022",
    "description": "The ISF Standard of Good Practice provides a comprehensive framework for information security management",
    "authority": "Information Security Forum",
    "publication_date": datetime(2022, 1, 1),
    "effective_date": datetime(2022, 1, 1),
    "metadata": {
        "total_areas": 14,
        "total_controls": 56,
        "scope": "Information Security Management"
    },
    "domains": [
        {
            "domain_id": "SG1",
            "name": "Security Governance",
            "description": "Establishing and maintaining an effective information security governance framework",
            "sort_order": 1,
            "categories": [
                {
                    "category_id": "SG1.1",
                    "name": "Information Security Strategy",
                    "description": "Developing and implementing information security strategy",
                    "sort_order": 1,
                    "controls": [
                        {
                            "control_id": "SG1.1.1",
                            "name": "Information Security Strategy Development",
                            "description": "Develop a comprehensive information security strategy aligned with business objectives",
                            "objective": "Ensure information security supports business goals and risk appetite",
                            "control_type": "strategic",
                            "risk_rating": "high",
                            "priority": "critical",
                            "automation_level": "manual",
                            "sort_order": 1,
                            "tags": ["strategy", "governance", "alignment"]
                        }
                    ]
                }
            ]
        },
        {
            "domain_id": "SG2",
            "name": "Information Risk Management",
            "description": "Managing information-related risks effectively",
            "sort_order": 2,
            "categories": [
                {
                    "category_id": "SG2.1",
                    "name": "Risk Assessment",
                    "description": "Conducting comprehensive risk assessments",
                    "sort_order": 1,
                    "controls": [
                        {
                            "control_id": "SG2.1.1",
                            "name": "Risk Assessment Process",
                            "description": "Establish and maintain a systematic risk assessment process",
                            "objective": "Identify, analyze, and evaluate information security risks",
                            "control_type": "process",
                            "risk_rating": "high",
                            "priority": "high",
                            "automation_level": "semi_automated",
                            "sort_order": 1,
                            "tags": ["risk", "assessment", "process"]
                        }
                    ]
                }
            ]
        }
    ]
}

# NIST CSF 2.0 Sample Data
NIST_CSF_2_0_SAMPLE = {
    "name": "NIST Cybersecurity Framework 2.0",
    "version": "2.0",
    "framework_type": "nist_csf_2_0",
    "description": "A voluntary framework consisting of standards, guidelines, and practices to promote the protection of critical infrastructure",
    "authority": "National Institute of Standards and Technology",
    "publication_date": datetime(2024, 2, 26),
    "effective_date": datetime(2024, 2, 26),
    "metadata": {
        "total_functions": 6,
        "total_categories": 22,
        "total_subcategories": 108,
        "scope": "Cybersecurity Risk Management"
    },
    "domains": [
        {
            "domain_id": "GV",
            "name": "Govern",
            "description": "The organization's cybersecurity risk management strategy, expectations, and policy are established, communicated, and monitored",
            "sort_order": 1,
            "categories": [
                {
                    "category_id": "GV.OC",
                    "name": "Organizational Context",
                    "description": "The circumstances that influence the organization's cybersecurity risk management decisions are understood",
                    "sort_order": 1,
                    "controls": [
                        {
                            "control_id": "GV.OC-01",
                            "name": "Organizational Mission and Business Objectives",
                            "description": "The organizational mission, objectives, stakeholders, and activities are understood and used to inform cybersecurity roles, responsibilities, and risk management decisions",
                            "objective": "Establish clear understanding of organizational context for cybersecurity decisions",
                            "control_type": "governance",
                            "risk_rating": "medium",
                            "priority": "high",
                            "automation_level": "manual",
                            "sort_order": 1,
                            "tags": ["governance", "mission", "objectives"]
                        }
                    ]
                }
            ]
        },
        {
            "domain_id": "ID",
            "name": "Identify",
            "description": "The organization's current cybersecurity posture is understood",
            "sort_order": 2,
            "categories": [
                {
                    "category_id": "ID.AM",
                    "name": "Asset Management",
                    "description": "Assets are identified and managed consistent with their relative importance to organizational objectives and the organization's risk strategy",
                    "sort_order": 1,
                    "controls": [
                        {
                            "control_id": "ID.AM-01",
                            "name": "Asset Inventory",
                            "description": "Inventories of hardware, software, systems, facilities, and personnel are maintained",
                            "objective": "Maintain comprehensive asset inventories to support risk management",
                            "control_type": "inventory",
                            "risk_rating": "high",
                            "priority": "critical",
                            "automation_level": "automated",
                            "sort_order": 1,
                            "tags": ["assets", "inventory", "hardware", "software"]
                        }
                    ]
                }
            ]
        }
    ]
}

# ISO/IEC 27001:2022 Sample Data
ISO_27001_2022_SAMPLE = {
    "name": "ISO/IEC 27001:2022 Information Security Management",
    "version": "2022",
    "framework_type": "iso_27001_2022",
    "description": "International standard for information security management systems (ISMS)",
    "authority": "International Organization for Standardization",
    "publication_date": datetime(2022, 10, 25),
    "effective_date": datetime(2022, 10, 25),
    "metadata": {
        "total_domains": 4,
        "total_controls": 93,
        "scope": "Information Security Management Systems"
    },
    "domains": [
        {
            "domain_id": "A.5",
            "name": "Organizational Controls",
            "description": "Controls related to organizational aspects of information security",
            "sort_order": 1,
            "categories": [
                {
                    "category_id": "A.5.1",
                    "name": "Information Security Policies",
                    "description": "Management direction and support for information security",
                    "sort_order": 1,
                    "controls": [
                        {
                            "control_id": "A.5.1.1",
                            "name": "Information Security Policy",
                            "description": "An information security policy shall be defined, approved by management, published and communicated to employees and relevant external parties",
                            "objective": "Provide management direction and support for information security",
                            "control_type": "policy",
                            "risk_rating": "high",
                            "priority": "critical",
                            "automation_level": "manual",
                            "sort_order": 1,
                            "tags": ["policy", "management", "communication"]
                        }
                    ]
                }
            ]
        },
        {
            "domain_id": "A.6",
            "name": "People Controls",
            "description": "Controls related to people and human resources security",
            "sort_order": 2,
            "categories": [
                {
                    "category_id": "A.6.1",
                    "name": "Screening",
                    "description": "Background verification checks on all candidates for employment",
                    "sort_order": 1,
                    "controls": [
                        {
                            "control_id": "A.6.1.1",
                            "name": "Screening",
                            "description": "Background verification checks on all candidates for employment shall be carried out in accordance with relevant laws, regulations and ethics",
                            "objective": "Ensure personnel are suitable and trustworthy",
                            "control_type": "preventive",
                            "risk_rating": "medium",
                            "priority": "medium",
                            "automation_level": "manual",
                            "sort_order": 1,
                            "tags": ["screening", "background", "personnel"]
                        }
                    ]
                }
            ]
        }
    ]
}

# CIS Controls v8 Sample Data
CIS_CONTROLS_V8_SAMPLE = {
    "name": "CIS Controls Version 8",
    "version": "8.0",
    "framework_type": "cis_controls_v8",
    "description": "A prioritized set of actions for cyber defense that provide specific and actionable ways to stop today's most pervasive and dangerous attacks",
    "authority": "Center for Internet Security",
    "publication_date": datetime(2021, 5, 18),
    "effective_date": datetime(2021, 5, 18),
    "metadata": {
        "total_controls": 18,
        "total_safeguards": 153,
        "scope": "Cyber Defense Actions"
    },
    "domains": [
        {
            "domain_id": "CIS1",
            "name": "Inventory and Control of Enterprise Assets",
            "description": "Actively manage all enterprise assets connected to the infrastructure",
            "sort_order": 1,
            "categories": [
                {
                    "category_id": "CIS1.1",
                    "name": "Asset Discovery",
                    "description": "Establish and maintain detailed enterprise asset inventory",
                    "sort_order": 1,
                    "controls": [
                        {
                            "control_id": "CIS1.1",
                            "name": "Establish and Maintain Detailed Enterprise Asset Inventory",
                            "description": "Establish and maintain a detailed inventory of all enterprise assets with the potential to store or process data",
                            "objective": "Ensure complete visibility of enterprise assets",
                            "control_type": "inventory",
                            "risk_rating": "critical",
                            "priority": "critical",
                            "automation_level": "automated",
                            "sort_order": 1,
                            "tags": ["inventory", "assets", "discovery"]
                        }
                    ]
                }
            ]
        },
        {
            "domain_id": "CIS2",
            "name": "Inventory and Control of Software Assets",
            "description": "Actively manage all software on the network",
            "sort_order": 2,
            "categories": [
                {
                    "category_id": "CIS2.1",
                    "name": "Software Inventory",
                    "description": "Establish and maintain software inventory",
                    "sort_order": 1,
                    "controls": [
                        {
                            "control_id": "CIS2.1",
                            "name": "Establish and Maintain Software Inventory",
                            "description": "Establish and maintain a detailed inventory of all licensed software installed on enterprise assets",
                            "objective": "Ensure complete visibility of software assets",
                            "control_type": "inventory",
                            "risk_rating": "high",
                            "priority": "high",
                            "automation_level": "automated",
                            "sort_order": 1,
                            "tags": ["software", "inventory", "licensing"]
                        }
                    ]
                }
            ]
        }
    ]
}

# All framework samples
FRAMEWORK_SAMPLES = {
    "isf_2022": ISF_2022_SAMPLE,
    "nist_csf_2_0": NIST_CSF_2_0_SAMPLE,
    "iso_27001_2022": ISO_27001_2022_SAMPLE,
    "cis_controls_v8": CIS_CONTROLS_V8_SAMPLE
}


def get_framework_sample(framework_type: str) -> Dict[str, Any]:
    """Get sample data for a specific framework type"""
    return FRAMEWORK_SAMPLES.get(framework_type)


def get_all_framework_samples() -> Dict[str, Dict[str, Any]]:
    """Get all framework sample data"""
    return FRAMEWORK_SAMPLES
