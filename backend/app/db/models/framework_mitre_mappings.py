"""
Framework-MITRE ATT&CK Integration Models
Maps cybersecurity framework controls to MITRE ATT&CK techniques
"""

import uuid
from datetime import datetime
from typing import Optional
from enum import Enum

from sqlalchemy import (
    Column, String, Text, Integer, Float, Boolean, DateTime, 
    ForeignKey, Index, CheckConstraint, UniqueConstraint, JSONB
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base
from app.db.mixins.audit import AuditMixin
from app.db.mixins.soft_delete import SoftDeleteMixin


class MappingType(Enum):
    """Types of control-technique mappings"""
    PREVENTIVE = "preventive"
    DETECTIVE = "detective"
    CORRECTIVE = "corrective"
    COMPENSATING = "compensating"


class MappingConfidence(Enum):
    """Confidence levels for mappings"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERIFIED = "verified"


class FrameworkMitreMapping(Base, AuditMixin, SoftDeleteMixin):
    """Mapping between framework controls and MITRE ATT&CK techniques"""
    
    __tablename__ = "framework_mitre_mappings"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Framework control reference
    control_id = Column(UUID(as_uuid=True), ForeignKey("framework_controls.id"), nullable=False)
    
    # MITRE technique reference
    technique_id = Column(UUID(as_uuid=True), ForeignKey("mitre_techniques.id"), nullable=False)
    
    # Mapping details
    mapping_type = Column(String(50), nullable=False)  # preventive, detective, etc.
    confidence_level = Column(String(50), nullable=False, default="medium")
    relevance_score = Column(Float, nullable=False, default=0.5)
    
    # Mapping rationale and context
    rationale = Column(Text)
    mapping_context = Column(JSONB)  # Additional context data
    
    # Validation and review
    is_validated = Column(Boolean, default=False)
    validated_by = Column(UUID(as_uuid=True))
    validated_at = Column(DateTime)
    validation_notes = Column(Text)
    
    # Effectiveness metrics
    effectiveness_score = Column(Float)  # How effective the control is against the technique
    coverage_percentage = Column(Float)  # What percentage of the technique is covered
    
    # Automation and detection
    automated_detection = Column(Boolean, default=False)
    detection_rules = Column(JSONB)  # Detection rule references
    
    # Relationships
    control = relationship("FrameworkControl", back_populates="mitre_mappings")
    technique = relationship("MitreTechnique", back_populates="framework_mappings")
    
    __table_args__ = (
        UniqueConstraint('control_id', 'technique_id', name='uq_control_technique_mapping'),
        Index('idx_mapping_control', 'control_id'),
        Index('idx_mapping_technique', 'technique_id'),
        Index('idx_mapping_confidence', 'confidence_level', 'relevance_score'),
        CheckConstraint('relevance_score >= 0 AND relevance_score <= 1', name='chk_relevance_score_range'),
        CheckConstraint('effectiveness_score >= 0 AND effectiveness_score <= 1', name='chk_effectiveness_score_range'),
        CheckConstraint('coverage_percentage >= 0 AND coverage_percentage <= 100', name='chk_coverage_percentage_range'),
    )


class FrameworkTacticCoverage(Base, AuditMixin, SoftDeleteMixin):
    """Framework coverage analysis for MITRE tactics"""
    
    __tablename__ = "framework_tactic_coverage"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Framework reference
    framework_id = Column(UUID(as_uuid=True), ForeignKey("cybersecurity_frameworks.id"), nullable=False)
    
    # MITRE tactic reference
    tactic_id = Column(UUID(as_uuid=True), ForeignKey("mitre_tactics.id"), nullable=False)
    
    # Coverage metrics
    total_techniques = Column(Integer, nullable=False, default=0)
    covered_techniques = Column(Integer, nullable=False, default=0)
    coverage_percentage = Column(Float, nullable=False, default=0.0)
    
    # Control statistics
    preventive_controls = Column(Integer, default=0)
    detective_controls = Column(Integer, default=0)
    corrective_controls = Column(Integer, default=0)
    
    # Effectiveness metrics
    average_effectiveness = Column(Float, default=0.0)
    weighted_coverage = Column(Float, default=0.0)  # Coverage weighted by technique criticality
    
    # Gap analysis
    critical_gaps = Column(JSONB)  # List of uncovered critical techniques
    improvement_recommendations = Column(JSONB)
    
    # Analysis metadata
    analysis_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    analysis_version = Column(String(50), default="1.0")
    
    # Relationships
    framework = relationship("CybersecurityFramework")
    tactic = relationship("MitreTactic")
    
    __table_args__ = (
        UniqueConstraint('framework_id', 'tactic_id', name='uq_framework_tactic_coverage'),
        Index('idx_coverage_framework', 'framework_id'),
        Index('idx_coverage_tactic', 'tactic_id'),
        Index('idx_coverage_percentage', 'coverage_percentage'),
        CheckConstraint('coverage_percentage >= 0 AND coverage_percentage <= 100', name='chk_tactic_coverage_range'),
        CheckConstraint('covered_techniques <= total_techniques', name='chk_covered_techniques_valid'),
    )


class AttackPathControlMapping(Base, AuditMixin, SoftDeleteMixin):
    """Mapping between attack paths and relevant controls"""
    
    __tablename__ = "attack_path_control_mappings"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Attack path reference (assuming attack paths have IDs)
    attack_path_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Framework control reference
    control_id = Column(UUID(as_uuid=True), ForeignKey("framework_controls.id"), nullable=False)
    
    # Mapping details
    path_step = Column(Integer)  # Which step in the attack path this control affects
    mitigation_type = Column(String(50))  # blocks, detects, slows, etc.
    effectiveness_rating = Column(String(50))  # high, medium, low
    
    # Impact analysis
    risk_reduction = Column(Float)  # How much this control reduces the path risk
    blast_radius_impact = Column(Float)  # Impact on blast radius calculation
    
    # Implementation status for this specific path
    implementation_priority = Column(String(50))  # critical, high, medium, low
    implementation_complexity = Column(String(50))  # simple, moderate, complex
    
    # Cost-benefit analysis
    implementation_cost = Column(Float)
    risk_mitigation_value = Column(Float)
    roi_score = Column(Float)  # Return on investment score
    
    # Relationships
    control = relationship("FrameworkControl")
    
    __table_args__ = (
        Index('idx_attack_path_control', 'attack_path_id', 'control_id'),
        Index('idx_control_attack_path', 'control_id'),
        Index('idx_implementation_priority', 'implementation_priority', 'effectiveness_rating'),
        CheckConstraint('risk_reduction >= 0 AND risk_reduction <= 1', name='chk_risk_reduction_range'),
        CheckConstraint('blast_radius_impact >= 0 AND blast_radius_impact <= 1', name='chk_blast_radius_impact_range'),
    )


class ControlThreatIntelligence(Base, AuditMixin, SoftDeleteMixin):
    """Threat intelligence enrichment for framework controls"""
    
    __tablename__ = "control_threat_intelligence"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Framework control reference
    control_id = Column(UUID(as_uuid=True), ForeignKey("framework_controls.id"), nullable=False)
    
    # Threat intelligence data
    threat_actors = Column(JSONB)  # List of relevant threat actors
    attack_patterns = Column(JSONB)  # Common attack patterns this control addresses
    threat_campaigns = Column(JSONB)  # Recent campaigns using techniques this control mitigates
    
    # Risk assessment
    threat_likelihood = Column(String(50))  # very_low, low, medium, high, very_high
    threat_impact = Column(String(50))  # very_low, low, medium, high, very_high
    overall_threat_score = Column(Float)  # Calculated threat score
    
    # Temporal analysis
    threat_trend = Column(String(50))  # increasing, stable, decreasing
    recent_activity = Column(Boolean, default=False)  # Recent threat activity observed
    
    # Intelligence sources
    intelligence_sources = Column(JSONB)  # Sources of threat intelligence
    confidence_score = Column(Float)  # Confidence in the intelligence
    
    # Update tracking
    last_intelligence_update = Column(DateTime, default=datetime.utcnow)
    intelligence_version = Column(String(50), default="1.0")
    
    # Relationships
    control = relationship("FrameworkControl", back_populates="threat_intelligence")
    
    __table_args__ = (
        Index('idx_control_threat_intel', 'control_id'),
        Index('idx_threat_score', 'overall_threat_score'),
        Index('idx_threat_activity', 'recent_activity', 'threat_trend'),
        CheckConstraint('overall_threat_score >= 0 AND overall_threat_score <= 1', name='chk_threat_score_range'),
        CheckConstraint('confidence_score >= 0 AND confidence_score <= 1', name='chk_confidence_score_range'),
    )


class FrameworkComplianceMetrics(Base, AuditMixin, SoftDeleteMixin):
    """Aggregated compliance metrics for frameworks"""
    
    __tablename__ = "framework_compliance_metrics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Framework reference
    framework_id = Column(UUID(as_uuid=True), ForeignKey("cybersecurity_frameworks.id"), nullable=False)
    
    # Organization reference (for multi-tenant support)
    organization_id = Column(UUID(as_uuid=True))
    
    # Compliance metrics
    total_controls = Column(Integer, nullable=False, default=0)
    implemented_controls = Column(Integer, nullable=False, default=0)
    partially_implemented_controls = Column(Integer, nullable=False, default=0)
    not_implemented_controls = Column(Integer, nullable=False, default=0)
    
    # Percentage calculations
    compliance_percentage = Column(Float, nullable=False, default=0.0)
    implementation_percentage = Column(Float, nullable=False, default=0.0)
    
    # Risk-based metrics
    critical_controls_implemented = Column(Integer, default=0)
    high_risk_controls_implemented = Column(Integer, default=0)
    risk_weighted_compliance = Column(Float, default=0.0)
    
    # MITRE coverage metrics
    mitre_techniques_covered = Column(Integer, default=0)
    mitre_tactics_covered = Column(Integer, default=0)
    mitre_coverage_percentage = Column(Float, default=0.0)
    
    # Trend analysis
    compliance_trend = Column(String(50))  # improving, stable, declining
    trend_percentage = Column(Float, default=0.0)  # Rate of change
    
    # Assessment metrics
    last_assessment_date = Column(DateTime)
    next_assessment_due = Column(DateTime)
    assessment_frequency = Column(String(50))  # monthly, quarterly, annually
    
    # Calculation metadata
    calculation_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    calculation_version = Column(String(50), default="1.0")
    
    # Relationships
    framework = relationship("CybersecurityFramework")
    
    __table_args__ = (
        UniqueConstraint('framework_id', 'organization_id', name='uq_framework_org_metrics'),
        Index('idx_metrics_framework', 'framework_id'),
        Index('idx_metrics_organization', 'organization_id'),
        Index('idx_compliance_percentage', 'compliance_percentage'),
        Index('idx_calculation_date', 'calculation_date'),
        CheckConstraint('compliance_percentage >= 0 AND compliance_percentage <= 100', name='chk_compliance_percentage_range'),
        CheckConstraint('implementation_percentage >= 0 AND implementation_percentage <= 100', name='chk_implementation_percentage_range'),
        CheckConstraint('mitre_coverage_percentage >= 0 AND mitre_coverage_percentage <= 100', name='chk_mitre_coverage_range'),
        CheckConstraint('implemented_controls + partially_implemented_controls + not_implemented_controls = total_controls', name='chk_control_counts_valid'),
    )


# Add relationships to existing models
def add_framework_mitre_relationships():
    """Add relationships to existing models"""
    
    # Add to FrameworkControl model
    from app.db.models.cybersecurity_frameworks import FrameworkControl
    FrameworkControl.mitre_mappings = relationship(
        "FrameworkMitreMapping", 
        back_populates="control", 
        cascade="all, delete-orphan"
    )
    FrameworkControl.threat_intelligence = relationship(
        "ControlThreatIntelligence", 
        back_populates="control", 
        cascade="all, delete-orphan"
    )
    
    # Add to MitreTechnique model
    from app.db.models.mitre import MitreTechnique
    MitreTechnique.framework_mappings = relationship(
        "FrameworkMitreMapping", 
        back_populates="technique", 
        cascade="all, delete-orphan"
    )
