"""
Cybersecurity Frameworks Database Models
Implements comprehensive models for ISF 2022, NIST CSF 2.0, ISO/IEC 27001:2022, and CIS Controls v8
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum

from sqlalchemy import (
    Column, String, Text, Integer, Float, Boolean, DateTime, 
    ForeignKey, Index, CheckConstraint, UniqueConstraint, JSONB
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base

from app.db.base import Base
from app.db.mixins.audit import AuditMixin
from app.db.mixins.soft_delete import SoftDeleteMixin


class FrameworkType(Enum):
    """Supported cybersecurity frameworks"""
    ISF_2022 = "isf_2022"
    NIST_CSF_2_0 = "nist_csf_2_0"
    ISO_27001_2022 = "iso_27001_2022"
    CIS_CONTROLS_V8 = "cis_controls_v8"


class ControlStatus(Enum):
    """Control implementation status"""
    NOT_IMPLEMENTED = "not_implemented"
    PARTIALLY_IMPLEMENTED = "partially_implemented"
    IMPLEMENTED = "implemented"
    VERIFIED = "verified"
    NON_COMPLIANT = "non_compliant"


class RiskLevel(Enum):
    """Risk level classification"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AutomationLevel(Enum):
    """Control automation level"""
    MANUAL = "manual"
    SEMI_AUTOMATED = "semi_automated"
    AUTOMATED = "automated"


# Framework Models
class CybersecurityFramework(Base, AuditMixin, SoftDeleteMixin):
    """Base cybersecurity framework model"""
    
    __tablename__ = "cybersecurity_frameworks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False, index=True)
    framework_type = Column(String(50), nullable=False, index=True)
    version = Column(String(50), nullable=False)
    description = Column(Text)
    authority = Column(String(255))  # e.g., "NIST", "ISO", "ISF"
    publication_date = Column(DateTime)
    effective_date = Column(DateTime)
    status = Column(String(50), default="active")
    metadata = Column(JSONB)
    
    # Relationships
    domains = relationship("FrameworkDomain", back_populates="framework", cascade="all, delete-orphan")
    controls = relationship("FrameworkControl", back_populates="framework", cascade="all, delete-orphan")
    
    __table_args__ = (
        UniqueConstraint('name', 'version', name='uq_framework_name_version'),
        Index('idx_framework_type_status', 'framework_type', 'status'),
    )


class FrameworkDomain(Base, AuditMixin, SoftDeleteMixin):
    """Framework domains/categories (e.g., NIST Functions, ISO Domains)"""
    
    __tablename__ = "framework_domains"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    framework_id = Column(UUID(as_uuid=True), ForeignKey("cybersecurity_frameworks.id"), nullable=False)
    domain_id = Column(String(100), nullable=False)  # e.g., "ID", "PR", "DE"
    name = Column(String(500), nullable=False)
    description = Column(Text)
    sort_order = Column(Integer, default=0)
    metadata = Column(JSONB)
    
    # Relationships
    framework = relationship("CybersecurityFramework", back_populates="domains")
    categories = relationship("FrameworkCategory", back_populates="domain", cascade="all, delete-orphan")
    controls = relationship("FrameworkControl", back_populates="domain")
    
    __table_args__ = (
        UniqueConstraint('framework_id', 'domain_id', name='uq_domain_framework_id'),
        Index('idx_domain_framework', 'framework_id', 'sort_order'),
    )


class FrameworkCategory(Base, AuditMixin, SoftDeleteMixin):
    """Framework categories/subcategories"""
    
    __tablename__ = "framework_categories"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    domain_id = Column(UUID(as_uuid=True), ForeignKey("framework_domains.id"), nullable=False)
    category_id = Column(String(100), nullable=False)  # e.g., "ID.AM", "PR.AC"
    name = Column(String(500), nullable=False)
    description = Column(Text)
    sort_order = Column(Integer, default=0)
    parent_category_id = Column(UUID(as_uuid=True), ForeignKey("framework_categories.id"))
    metadata = Column(JSONB)
    
    # Relationships
    domain = relationship("FrameworkDomain", back_populates="categories")
    parent_category = relationship("FrameworkCategory", remote_side=[id])
    controls = relationship("FrameworkControl", back_populates="category")
    
    __table_args__ = (
        UniqueConstraint('domain_id', 'category_id', name='uq_category_domain_id'),
        Index('idx_category_domain', 'domain_id', 'sort_order'),
    )


class FrameworkControl(Base, AuditMixin, SoftDeleteMixin):
    """Individual framework controls/requirements"""
    
    __tablename__ = "framework_controls"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    framework_id = Column(UUID(as_uuid=True), ForeignKey("cybersecurity_frameworks.id"), nullable=False)
    domain_id = Column(UUID(as_uuid=True), ForeignKey("framework_domains.id"))
    category_id = Column(UUID(as_uuid=True), ForeignKey("framework_categories.id"))
    
    # Control identification
    control_id = Column(String(100), nullable=False)  # e.g., "ID.AM-1", "A.5.1"
    name = Column(String(500), nullable=False)
    description = Column(Text)
    objective = Column(Text)
    implementation_guidance = Column(Text)
    
    # Control properties
    control_type = Column(String(50))  # preventive, detective, corrective
    automation_level = Column(String(50))
    risk_rating = Column(String(50))
    priority = Column(String(50))
    
    # Assessment criteria
    assessment_criteria = Column(JSONB)
    testing_procedures = Column(JSONB)
    evidence_requirements = Column(JSONB)
    
    # Hierarchy and relationships
    parent_control_id = Column(UUID(as_uuid=True), ForeignKey("framework_controls.id"))
    sort_order = Column(Integer, default=0)
    
    # Additional metadata
    references = Column(JSONB)  # External references
    tags = Column(JSONB)  # Searchable tags
    metadata = Column(JSONB)
    
    # Relationships
    framework = relationship("CybersecurityFramework", back_populates="controls")
    domain = relationship("FrameworkDomain", back_populates="controls")
    category = relationship("FrameworkCategory", back_populates="controls")
    parent_control = relationship("FrameworkControl", remote_side=[id])
    
    # Implementation tracking
    implementations = relationship("ControlImplementation", back_populates="control", cascade="all, delete-orphan")
    assessments = relationship("ControlAssessment", back_populates="control", cascade="all, delete-orphan")
    
    __table_args__ = (
        UniqueConstraint('framework_id', 'control_id', name='uq_control_framework_id'),
        Index('idx_control_framework', 'framework_id', 'sort_order'),
        Index('idx_control_type_risk', 'control_type', 'risk_rating'),
        Index('idx_control_search', 'name', 'description'),
    )


class ControlImplementation(Base, AuditMixin, SoftDeleteMixin):
    """Control implementation tracking"""
    
    __tablename__ = "control_implementations"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    control_id = Column(UUID(as_uuid=True), ForeignKey("framework_controls.id"), nullable=False)
    organization_id = Column(UUID(as_uuid=True))  # Link to organization if multi-tenant
    
    # Implementation details
    status = Column(String(50), nullable=False, default="not_implemented")
    implementation_date = Column(DateTime)
    target_completion_date = Column(DateTime)
    responsible_party = Column(String(255))
    
    # Implementation evidence
    evidence = Column(JSONB)  # List of evidence items
    documentation = Column(JSONB)  # Links to documentation
    automated_checks = Column(JSONB)  # Automated validation rules
    
    # Gap analysis
    gaps = Column(JSONB)  # Identified gaps
    remediation_plan = Column(Text)
    remediation_priority = Column(String(50))
    
    # Cost and effort
    implementation_cost = Column(Float)
    effort_estimate = Column(Float)  # Hours
    
    # Relationships
    control = relationship("FrameworkControl", back_populates="implementations")
    assessments = relationship("ControlAssessment", back_populates="implementation")
    
    __table_args__ = (
        Index('idx_implementation_status', 'status', 'target_completion_date'),
        Index('idx_implementation_org', 'organization_id', 'status'),
    )


class ControlAssessment(Base, AuditMixin, SoftDeleteMixin):
    """Control assessment results"""
    
    __tablename__ = "control_assessments"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    control_id = Column(UUID(as_uuid=True), ForeignKey("framework_controls.id"), nullable=False)
    implementation_id = Column(UUID(as_uuid=True), ForeignKey("control_implementations.id"))
    
    # Assessment details
    assessment_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    assessor = Column(String(255), nullable=False)
    assessment_type = Column(String(50))  # internal, external, automated
    
    # Results
    status = Column(String(50), nullable=False)
    effectiveness_rating = Column(String(50))  # effective, partially_effective, ineffective
    maturity_level = Column(Integer)  # 1-5 maturity scale
    confidence_level = Column(String(50))  # high, medium, low
    
    # Findings
    findings = Column(JSONB)  # Detailed findings
    recommendations = Column(JSONB)  # Recommendations for improvement
    evidence_reviewed = Column(JSONB)  # Evidence that was reviewed
    
    # Scoring
    compliance_score = Column(Float)  # 0-100 compliance score
    risk_score = Column(Float)  # Risk score if non-compliant
    
    # Next assessment
    next_assessment_date = Column(DateTime)
    assessment_frequency = Column(String(50))  # annual, quarterly, monthly
    
    # Relationships
    control = relationship("FrameworkControl", back_populates="assessments")
    implementation = relationship("ControlImplementation", back_populates="assessments")
    
    __table_args__ = (
        Index('idx_assessment_date_status', 'assessment_date', 'status'),
        Index('idx_assessment_control', 'control_id', 'assessment_date'),
    )
