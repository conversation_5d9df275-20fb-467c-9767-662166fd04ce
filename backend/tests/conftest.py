"""Test configuration and fixtures for pytest."""

import os
import pytest
import uuid
from typing import Generator, Dict, Any
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from fastapi.testclient import TestClient
from app.main import app
from app.db.base import Base
from app.db.session import get_db
from app.db.models.user import User, UserRole
from app.db.models.cybersecurity_frameworks import (
    CybersecurityFramework,
    FrameworkDomain,
    FrameworkCategory,
    FrameworkControl,
    ControlImplementation,
    ControlAssessment
)
from app.services.auth_service import AuthService
from app.services.user_service import UserService
from app.services.cybersecurity_frameworks_service import CybersecurityFrameworksService
from app.core.security import get_password_hash
from app.config import settings
from app.data.framework_samples import FRAMEWORK_SAMPLES

# Test database URL
TEST_DATABASE_URL = "sqlite:///./test.db"

# Create test engine
engine = create_engine(
    TEST_DATABASE_URL,
    connect_args={"check_same_thread": False}
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def db_engine():
    """Create test database engine."""
    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def db_session(db_engine) -> Generator[Session, None, None]:
    """Create test database session."""
    connection = db_engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    
    yield session
    
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture(scope="function")
def client(db_session: Session) -> Generator[TestClient, None, None]:
    """Create test client with database session override."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()


@pytest.fixture
def auth_service(db_session: Session) -> AuthService:
    """Create auth service instance."""
    return AuthService(db_session)


@pytest.fixture
def user_service(db_session: Session) -> UserService:
    """Create user service instance."""
    return UserService(db_session)


@pytest.fixture
def sample_user_data() -> Dict[str, Any]:
    """Sample user data for testing."""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "full_name": "Test User",
        "password": "TestPassword123!",
        "phone_number": "+1234567890",
        "department": "Security",
        "job_title": "Analyst",
        "timezone": "UTC",
        "language": "en",
        "theme": "dark",
        "is_active": True,
        "require_password_change": False,
        "roles": ["analyst"]
    }


@pytest.fixture
def admin_user_data() -> Dict[str, Any]:
    """Admin user data for testing."""
    return {
        "username": "admin",
        "email": "<EMAIL>",
        "full_name": "Admin User",
        "password": "AdminPassword123!",
        "phone_number": "+1234567891",
        "department": "Security",
        "job_title": "Administrator",
        "timezone": "UTC",
        "language": "en",
        "theme": "dark",
        "is_active": True,
        "require_password_change": False,
        "roles": ["admin"]
    }


@pytest.fixture
def system_roles(db_session: Session) -> None:
    """Create system roles in test database."""
    UserRole.create_system_roles(db_session)


@pytest.fixture
def test_user(db_session: Session, system_roles, sample_user_data) -> User:
    """Create a test user in the database."""
    from app.schemas.user import UserCreate
    from app.services.user_service import UserService
    
    user_service = UserService(db_session)
    user_create = UserCreate(**sample_user_data)
    user = user_service.create_user(user_create)
    
    return user


@pytest.fixture
def admin_user(db_session: Session, system_roles, admin_user_data) -> User:
    """Create an admin user in the database."""
    from app.schemas.user import UserCreate
    from app.services.user_service import UserService
    
    user_service = UserService(db_session)
    user_create = UserCreate(**admin_user_data)
    user = user_service.create_user(user_create)
    
    return user


@pytest.fixture
def authenticated_headers(client: TestClient, test_user: User) -> Dict[str, str]:
    """Get authentication headers for test user."""
    login_data = {
        "username": test_user.username,
        "password": "TestPassword123!",
        "remember_me": False
    }
    
    response = client.post("/api/v1/auth/login", json=login_data)
    assert response.status_code == 200
    
    token_data = response.json()
    access_token = token_data["tokens"]["access_token"]
    
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def admin_headers(client: TestClient, admin_user: User) -> Dict[str, str]:
    """Get authentication headers for admin user."""
    login_data = {
        "username": admin_user.username,
        "password": "AdminPassword123!",
        "remember_me": False
    }
    
    response = client.post("/api/v1/auth/login", json=login_data)
    assert response.status_code == 200
    
    token_data = response.json()
    access_token = token_data["tokens"]["access_token"]
    
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def mock_request_context():
    """Mock request context for testing."""
    class MockClient:
        host = "127.0.0.1"
    
    class MockRequest:
        client = MockClient()
        headers = {"user-agent": "test-client"}
        url = type('obj', (object,), {'path': '/test'})()
        method = "GET"
    
    return MockRequest()


# Test data factories
class UserFactory:
    """Factory for creating test users."""
    
    @staticmethod
    def create_user_data(
        username: str = None,
        email: str = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Create user data with optional overrides."""
        base_data = {
            "username": username or f"user_{uuid.uuid4().hex[:8]}",
            "email": email or f"user_{uuid.uuid4().hex[:8]}@example.com",
            "full_name": "Test User",
            "password": "TestPassword123!",
            "phone_number": "+1234567890",
            "department": "Security",
            "job_title": "Analyst",
            "timezone": "UTC",
            "language": "en",
            "theme": "dark",
            "is_active": True,
            "require_password_change": False,
            "roles": ["analyst"]
        }
        base_data.update(kwargs)
        return base_data


class LoginAttemptFactory:
    """Factory for creating login attempt data."""
    
    @staticmethod
    def create_attempt_data(
        username: str = "testuser",
        success: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """Create login attempt data."""
        base_data = {
            "username": username,
            "success": success,
            "ip_address": "127.0.0.1",
            "user_agent": "test-client",
            "failure_reason": None if success else "invalid_credentials"
        }
        base_data.update(kwargs)
        return base_data


# Test utilities
def assert_user_response(user_data: Dict[str, Any], expected_data: Dict[str, Any]):
    """Assert user response matches expected data."""
    assert user_data["username"] == expected_data["username"]
    assert user_data["email"] == expected_data["email"]
    assert user_data["full_name"] == expected_data["full_name"]
    assert user_data["is_active"] == expected_data.get("is_active", True)


def assert_error_response(response_data: Dict[str, Any], expected_detail: str):
    """Assert error response format."""
    assert "detail" in response_data
    assert response_data["detail"] == expected_detail


# Pytest markers
pytest_plugins = []

# Cybersecurity Framework Fixtures
@pytest.fixture
def frameworks_service(db_session: Session) -> CybersecurityFrameworksService:
    """Create cybersecurity frameworks service instance."""
    return CybersecurityFrameworksService(db_session)


@pytest.fixture
def sample_framework(db_session: Session, test_user: User) -> CybersecurityFramework:
    """Create a sample framework for testing."""
    framework = CybersecurityFramework(
        name="Test Framework",
        framework_type="nist_csf_2_0",
        version="1.0",
        description="Test framework for unit tests",
        authority="Test Authority",
        created_by=test_user.id,
        updated_by=test_user.id
    )

    db_session.add(framework)
    db_session.commit()
    db_session.refresh(framework)

    return framework


@pytest.fixture
def sample_domain(db_session: Session, sample_framework: CybersecurityFramework, test_user: User) -> FrameworkDomain:
    """Create a sample domain for testing."""
    domain = FrameworkDomain(
        framework_id=sample_framework.id,
        domain_id="TEST",
        name="Test Domain",
        description="Test domain description",
        sort_order=1,
        created_by=test_user.id,
        updated_by=test_user.id
    )

    db_session.add(domain)
    db_session.commit()
    db_session.refresh(domain)

    return domain


@pytest.fixture
def sample_control(
    db_session: Session,
    sample_framework: CybersecurityFramework,
    sample_domain: FrameworkDomain,
    test_user: User
) -> FrameworkControl:
    """Create a sample control for testing."""
    control = FrameworkControl(
        framework_id=sample_framework.id,
        domain_id=sample_domain.id,
        control_id="TEST-01",
        name="Test Control",
        description="Test control description",
        objective="Test control objective",
        control_type="preventive",
        automation_level="manual",
        risk_rating="medium",
        priority="high",
        sort_order=1,
        tags=["test", "sample"],
        created_by=test_user.id,
        updated_by=test_user.id
    )

    db_session.add(control)
    db_session.commit()
    db_session.refresh(control)

    return control


@pytest.fixture
def sample_implementation(
    db_session: Session,
    sample_control: FrameworkControl,
    test_user: User
) -> ControlImplementation:
    """Create a sample implementation for testing."""
    implementation = ControlImplementation(
        control_id=sample_control.id,
        organization_id=uuid.uuid4(),
        status="partially_implemented",
        responsible_party="Test Team",
        evidence=[{"type": "document", "url": "test.pdf"}],
        gaps=[{"description": "Missing automation", "priority": "medium"}],
        remediation_plan="Implement automated scanning",
        remediation_priority="medium",
        implementation_cost=5000.0,
        effort_estimate=40.0,
        created_by=test_user.id,
        updated_by=test_user.id
    )

    db_session.add(implementation)
    db_session.commit()
    db_session.refresh(implementation)

    return implementation


@pytest.fixture
def framework_samples():
    """Fixture providing framework sample data."""
    return FRAMEWORK_SAMPLES


@pytest.fixture
def nist_csf_sample():
    """Fixture providing NIST CSF sample data."""
    return FRAMEWORK_SAMPLES["nist_csf_2_0"]


# Framework test data builder
class FrameworkTestDataBuilder:
    """Builder class for creating framework test data."""

    def __init__(self, db_session: Session, user_id: uuid.UUID):
        self.db_session = db_session
        self.user_id = user_id

    def create_framework(self, **kwargs) -> CybersecurityFramework:
        """Create a framework with default values."""
        defaults = {
            "name": "Test Framework",
            "framework_type": "nist_csf_2_0",
            "version": "1.0",
            "description": "Test framework",
            "authority": "Test Authority",
            "created_by": self.user_id,
            "updated_by": self.user_id
        }
        defaults.update(kwargs)

        framework = CybersecurityFramework(**defaults)
        self.db_session.add(framework)
        self.db_session.commit()
        self.db_session.refresh(framework)

        return framework

    def create_control(self, framework_id: uuid.UUID, **kwargs) -> FrameworkControl:
        """Create a control with default values."""
        defaults = {
            "framework_id": framework_id,
            "control_id": f"TEST-{uuid.uuid4().hex[:8]}",
            "name": "Test Control",
            "description": "Test control description",
            "control_type": "preventive",
            "risk_rating": "medium",
            "automation_level": "manual",
            "created_by": self.user_id,
            "updated_by": self.user_id
        }
        defaults.update(kwargs)

        control = FrameworkControl(**defaults)
        self.db_session.add(control)
        self.db_session.commit()
        self.db_session.refresh(control)

        return control


@pytest.fixture
def framework_test_builder(db_session: Session, test_user: User) -> FrameworkTestDataBuilder:
    """Fixture providing framework test data builder."""
    return FrameworkTestDataBuilder(db_session, test_user.id)


# Configure test environment
os.environ["TESTING"] = "true"
os.environ["DATABASE_URL"] = TEST_DATABASE_URL
os.environ["SECRET_KEY"] = "test-secret-key-for-testing-only"
os.environ["REDIS_URL"] = "redis://localhost:6379/1"
