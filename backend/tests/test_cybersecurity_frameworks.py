"""
Comprehensive tests for cybersecurity frameworks
Tests models, API endpoints, services, and integration scenarios
"""

import pytest
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.db.models.cybersecurity_frameworks import (
    CybersecurityFramework,
    FrameworkDomain,
    FrameworkCategory,
    FrameworkControl,
    ControlImplementation,
    ControlAssessment,
    FrameworkType,
    ControlStatus,
    RiskLevel
)
from app.schemas.cybersecurity_frameworks import (
    CybersecurityFrameworkCreate,
    FrameworkControlCreate,
    ControlImplementationCreate,
    ControlAssessmentCreate
)
from app.services.cybersecurity_frameworks_service import CybersecurityFrameworksService
from app.data.framework_samples import FRAMEWORK_SAMPLES


class TestCybersecurityFrameworkModels:
    """Test cybersecurity framework database models"""
    
    def test_framework_creation(self, db: Session):
        """Test creating a cybersecurity framework"""
        framework = CybersecurityFramework(
            name="Test Framework",
            framework_type=FrameworkType.NIST_CSF_2_0.value,
            version="1.0",
            description="Test framework description",
            authority="Test Authority",
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        
        db.add(framework)
        db.commit()
        db.refresh(framework)
        
        assert framework.id is not None
        assert framework.name == "Test Framework"
        assert framework.framework_type == "nist_csf_2_0"
        assert framework.version == "1.0"
        assert framework.created_at is not None
        assert framework.updated_at is not None
    
    def test_framework_domain_relationship(self, db: Session):
        """Test framework-domain relationship"""
        framework = CybersecurityFramework(
            name="Test Framework",
            framework_type=FrameworkType.NIST_CSF_2_0.value,
            version="1.0",
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        db.add(framework)
        db.flush()
        
        domain = FrameworkDomain(
            framework_id=framework.id,
            domain_id="TEST",
            name="Test Domain",
            description="Test domain description",
            sort_order=1,
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        db.add(domain)
        db.commit()
        
        # Test relationship
        assert len(framework.domains) == 1
        assert framework.domains[0].name == "Test Domain"
        assert domain.framework.name == "Test Framework"
    
    def test_control_hierarchy(self, db: Session):
        """Test control parent-child relationships"""
        framework = CybersecurityFramework(
            name="Test Framework",
            framework_type=FrameworkType.ISO_27001_2022.value,
            version="1.0",
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        db.add(framework)
        db.flush()
        
        parent_control = FrameworkControl(
            framework_id=framework.id,
            control_id="A.5",
            name="Parent Control",
            description="Parent control description",
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        db.add(parent_control)
        db.flush()
        
        child_control = FrameworkControl(
            framework_id=framework.id,
            control_id="A.5.1",
            name="Child Control",
            description="Child control description",
            parent_control_id=parent_control.id,
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        db.add(child_control)
        db.commit()
        
        # Test hierarchy
        assert child_control.parent_control.name == "Parent Control"
    
    def test_implementation_tracking(self, db: Session):
        """Test control implementation tracking"""
        framework = CybersecurityFramework(
            name="Test Framework",
            framework_type=FrameworkType.CIS_CONTROLS_V8.value,
            version="1.0",
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        db.add(framework)
        db.flush()
        
        control = FrameworkControl(
            framework_id=framework.id,
            control_id="CIS1.1",
            name="Test Control",
            description="Test control description",
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        db.add(control)
        db.flush()
        
        implementation = ControlImplementation(
            control_id=control.id,
            status=ControlStatus.IMPLEMENTED.value,
            implementation_date=datetime.utcnow(),
            responsible_party="Test Team",
            evidence=[{"type": "document", "url": "test.pdf"}],
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        db.add(implementation)
        db.commit()
        
        # Test implementation relationship
        assert len(control.implementations) == 1
        assert control.implementations[0].status == "implemented"
        assert implementation.control.name == "Test Control"
    
    def test_assessment_scoring(self, db: Session):
        """Test control assessment with scoring"""
        framework = CybersecurityFramework(
            name="Test Framework",
            framework_type=FrameworkType.NIST_CSF_2_0.value,
            version="1.0",
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        db.add(framework)
        db.flush()
        
        control = FrameworkControl(
            framework_id=framework.id,
            control_id="ID.AM-01",
            name="Asset Inventory",
            description="Maintain asset inventory",
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        db.add(control)
        db.flush()
        
        assessment = ControlAssessment(
            control_id=control.id,
            assessment_date=datetime.utcnow(),
            assessor="Test Assessor",
            assessment_type="internal",
            status=ControlStatus.IMPLEMENTED.value,
            effectiveness_rating="effective",
            maturity_level=4,
            confidence_level="high",
            compliance_score=85.5,
            risk_score=15.0,
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        db.add(assessment)
        db.commit()
        
        # Test assessment
        assert assessment.compliance_score == 85.5
        assert assessment.maturity_level == 4
        assert assessment.control.name == "Asset Inventory"


class TestCybersecurityFrameworkAPI:
    """Test cybersecurity framework API endpoints"""
    
    def setup_method(self):
        """Setup test client"""
        self.client = TestClient(app)
        self.base_url = "/api/v1/frameworks"
    
    @pytest.fixture
    def auth_headers(self):
        """Mock authentication headers"""
        return {"Authorization": "Bearer test-token"}
    
    def test_create_framework(self, auth_headers):
        """Test creating a framework via API"""
        framework_data = {
            "name": "Test API Framework",
            "framework_type": "nist_csf_2_0",
            "version": "1.0",
            "description": "Test framework via API",
            "authority": "Test Authority"
        }
        
        with patch('app.api.deps.get_current_active_user') as mock_user:
            mock_user.return_value = Mock(id=uuid.uuid4())
            
            response = self.client.post(
                f"{self.base_url}/frameworks",
                json=framework_data,
                headers=auth_headers
            )
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Test API Framework"
        assert data["framework_type"] == "nist_csf_2_0"
    
    def test_list_frameworks(self, auth_headers):
        """Test listing frameworks with filtering"""
        with patch('app.api.deps.get_current_active_user') as mock_user:
            mock_user.return_value = Mock(id=uuid.uuid4())
            
            response = self.client.get(
                f"{self.base_url}/frameworks?framework_type=nist_csf_2_0",
                headers=auth_headers
            )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_framework_search(self, auth_headers):
        """Test advanced framework search"""
        search_data = {
            "query": "asset inventory",
            "framework_types": ["nist_csf_2_0", "iso_27001_2022"],
            "risk_levels": ["high", "critical"],
            "page": 1,
            "size": 20
        }
        
        with patch('app.api.deps.get_current_active_user') as mock_user:
            mock_user.return_value = Mock(id=uuid.uuid4())
            
            response = self.client.post(
                f"{self.base_url}/search",
                json=search_data,
                headers=auth_headers
            )
        
        assert response.status_code == 200
        data = response.json()
        assert "total" in data
        assert "results" in data
        assert "page" in data
    
    def test_compliance_gap_analysis(self, auth_headers):
        """Test compliance gap analysis endpoint"""
        analytics_data = {
            "framework_ids": [str(uuid.uuid4())],
            "date_from": "2024-01-01T00:00:00",
            "date_to": "2024-12-31T23:59:59"
        }
        
        with patch('app.api.deps.get_current_active_user') as mock_user:
            mock_user.return_value = Mock(id=uuid.uuid4())
            
            response = self.client.post(
                f"{self.base_url}/analytics/gap-analysis",
                json=analytics_data,
                headers=auth_headers
            )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_compliance_dashboard(self, auth_headers):
        """Test compliance dashboard endpoint"""
        with patch('app.api.deps.get_current_active_user') as mock_user:
            mock_user.return_value = Mock(id=uuid.uuid4())
            
            response = self.client.get(
                f"{self.base_url}/analytics/dashboard",
                headers=auth_headers
            )
        
        assert response.status_code == 200
        data = response.json()
        assert "summary" in data
        assert "framework_breakdown" in data
        assert "risk_distribution" in data
    
    def test_cross_framework_mapping(self, auth_headers):
        """Test cross-framework mapping endpoint"""
        source_id = str(uuid.uuid4())
        target_id = str(uuid.uuid4())
        
        with patch('app.api.deps.get_current_active_user') as mock_user:
            mock_user.return_value = Mock(id=uuid.uuid4())
            
            response = self.client.get(
                f"{self.base_url}/mapping/cross-framework?source_framework_id={source_id}&target_framework_id={target_id}",
                headers=auth_headers
            )
        
        assert response.status_code == 200
        data = response.json()
        assert "source_framework" in data
        assert "target_framework" in data
        assert "mappings" in data


class TestCybersecurityFrameworkService:
    """Test cybersecurity framework service layer"""
    
    def test_import_framework_data(self, db: Session):
        """Test importing framework data"""
        service = CybersecurityFrameworksService(db)
        user_id = uuid.uuid4()
        
        # Use sample NIST CSF data
        framework_data = FRAMEWORK_SAMPLES["nist_csf_2_0"]
        
        # Import framework
        framework = service.import_framework_data(
            FrameworkType.NIST_CSF_2_0,
            framework_data,
            user_id
        )
        
        assert framework.name == "NIST Cybersecurity Framework 2.0"
        assert framework.framework_type == "nist_csf_2_0"
        assert len(framework.domains) > 0
        assert len(framework.controls) > 0
    
    def test_bulk_update_implementations(self, db: Session):
        """Test bulk updating control implementations"""
        service = CybersecurityFrameworksService(db)
        user_id = uuid.uuid4()
        
        # Create test framework and control
        framework = CybersecurityFramework(
            name="Test Framework",
            framework_type=FrameworkType.NIST_CSF_2_0.value,
            version="1.0",
            created_by=user_id,
            updated_by=user_id
        )
        db.add(framework)
        db.flush()
        
        control = FrameworkControl(
            framework_id=framework.id,
            control_id="TEST-01",
            name="Test Control",
            created_by=user_id,
            updated_by=user_id
        )
        db.add(control)
        db.flush()
        
        # Bulk update
        updates = [
            {
                "control_id": control.id,
                "status": "implemented",
                "implementation_date": datetime.utcnow(),
                "responsible_party": "Test Team"
            }
        ]
        
        result = service.bulk_update_implementations(updates, user_id)
        
        assert result["created"] == 1
        assert result["updated"] == 0
        assert len(result["errors"]) == 0
    
    def test_generate_compliance_report(self, db: Session):
        """Test generating compliance report"""
        service = CybersecurityFrameworksService(db)
        user_id = uuid.uuid4()
        
        # Create test data
        framework = CybersecurityFramework(
            name="Test Framework",
            framework_type=FrameworkType.ISO_27001_2022.value,
            version="1.0",
            created_by=user_id,
            updated_by=user_id
        )
        db.add(framework)
        db.flush()
        
        control = FrameworkControl(
            framework_id=framework.id,
            control_id="A.5.1.1",
            name="Information Security Policy",
            risk_rating="high",
            created_by=user_id,
            updated_by=user_id
        )
        db.add(control)
        db.flush()
        
        implementation = ControlImplementation(
            control_id=control.id,
            status="implemented",
            implementation_date=datetime.utcnow(),
            created_by=user_id,
            updated_by=user_id
        )
        db.add(implementation)
        db.commit()
        
        # Generate report
        report = service.generate_compliance_report(framework.id)
        
        assert report["framework"]["name"] == "Test Framework"
        assert report["summary"]["total_controls"] == 1
        assert report["summary"]["implemented"] == 1
        assert report["summary"]["compliance_percentage"] == 100.0
    
    def test_suggest_control_mappings(self, db: Session):
        """Test ML-powered control mapping suggestions"""
        service = CybersecurityFrameworksService(db)
        user_id = uuid.uuid4()
        
        # Create two frameworks with similar controls
        framework1 = CybersecurityFramework(
            name="Framework 1",
            framework_type=FrameworkType.NIST_CSF_2_0.value,
            version="1.0",
            created_by=user_id,
            updated_by=user_id
        )
        framework2 = CybersecurityFramework(
            name="Framework 2",
            framework_type=FrameworkType.ISO_27001_2022.value,
            version="1.0",
            created_by=user_id,
            updated_by=user_id
        )
        db.add_all([framework1, framework2])
        db.flush()
        
        control1 = FrameworkControl(
            framework_id=framework1.id,
            control_id="ID.AM-01",
            name="Asset Inventory",
            description="Maintain detailed asset inventory",
            control_type="inventory",
            created_by=user_id,
            updated_by=user_id
        )
        control2 = FrameworkControl(
            framework_id=framework2.id,
            control_id="A.8.1.1",
            name="Inventory of Assets",
            description="Maintain inventory of assets",
            control_type="inventory",
            created_by=user_id,
            updated_by=user_id
        )
        db.add_all([control1, control2])
        db.commit()
        
        # Test mapping suggestions
        mappings = service.suggest_control_mappings(
            framework1.id,
            framework2.id,
            confidence_threshold=0.5
        )
        
        assert len(mappings) > 0
        assert mappings[0]["source_control"]["control_id"] == "ID.AM-01"
        assert len(mappings[0]["suggested_mappings"]) > 0


class TestFrameworkIntegration:
    """Integration tests for cybersecurity frameworks"""
    
    def test_end_to_end_framework_lifecycle(self, db: Session):
        """Test complete framework lifecycle"""
        service = CybersecurityFrameworksService(db)
        user_id = uuid.uuid4()
        
        # 1. Import framework
        framework_data = FRAMEWORK_SAMPLES["cis_controls_v8"]
        framework = service.import_framework_data(
            FrameworkType.CIS_CONTROLS_V8,
            framework_data,
            user_id
        )
        
        # 2. Create implementation
        control = framework.controls[0]
        implementation = ControlImplementation(
            control_id=control.id,
            status="partially_implemented",
            target_completion_date=datetime.utcnow() + timedelta(days=30),
            responsible_party="Security Team",
            gaps=[{"description": "Missing automated scanning"}],
            created_by=user_id,
            updated_by=user_id
        )
        db.add(implementation)
        db.flush()
        
        # 3. Create assessment
        assessment = ControlAssessment(
            control_id=control.id,
            implementation_id=implementation.id,
            assessment_date=datetime.utcnow(),
            assessor="Internal Auditor",
            assessment_type="internal",
            status="partially_implemented",
            maturity_level=3,
            compliance_score=75.0,
            findings=[{"finding": "Good progress, needs automation"}],
            created_by=user_id,
            updated_by=user_id
        )
        db.add(assessment)
        db.commit()
        
        # 4. Generate compliance report
        report = service.generate_compliance_report(framework.id)
        
        # Verify end-to-end flow
        assert report["framework"]["name"] == framework_data["name"]
        assert report["summary"]["partially_implemented"] == 1
        assert len(report["risk_analysis"]["medium_risk_gaps"]) > 0
        assert len(report["recommendations"]) > 0
    
    def test_performance_with_large_dataset(self, db: Session):
        """Test performance with large number of controls"""
        service = CybersecurityFrameworksService(db)
        user_id = uuid.uuid4()
        
        # Create framework with many controls
        framework = CybersecurityFramework(
            name="Large Framework",
            framework_type=FrameworkType.NIST_CSF_2_0.value,
            version="1.0",
            created_by=user_id,
            updated_by=user_id
        )
        db.add(framework)
        db.flush()
        
        # Create 100 controls
        controls = []
        for i in range(100):
            control = FrameworkControl(
                framework_id=framework.id,
                control_id=f"TEST-{i:03d}",
                name=f"Test Control {i}",
                description=f"Test control description {i}",
                risk_rating="medium",
                created_by=user_id,
                updated_by=user_id
            )
            controls.append(control)
        
        db.add_all(controls)
        db.commit()
        
        # Test performance of compliance report generation
        import time
        start_time = time.time()
        
        report = service.generate_compliance_report(framework.id)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should complete within reasonable time (< 5 seconds)
        assert execution_time < 5.0
        assert report["summary"]["total_controls"] == 100


# Test fixtures and utilities
@pytest.fixture
def sample_framework_data():
    """Fixture providing sample framework data"""
    return {
        "name": "Test Framework",
        "framework_type": "nist_csf_2_0",
        "version": "1.0",
        "description": "Test framework for unit tests",
        "authority": "Test Authority",
        "domains": [
            {
                "domain_id": "TEST",
                "name": "Test Domain",
                "description": "Test domain description",
                "categories": [
                    {
                        "category_id": "TEST.CAT",
                        "name": "Test Category",
                        "description": "Test category description",
                        "controls": [
                            {
                                "control_id": "TEST.CAT-01",
                                "name": "Test Control",
                                "description": "Test control description",
                                "objective": "Test control objective",
                                "control_type": "preventive",
                                "risk_rating": "medium",
                                "automation_level": "manual"
                            }
                        ]
                    }
                ]
            }
        ]
    }


@pytest.fixture
def mock_user():
    """Fixture providing mock user"""
    user = Mock()
    user.id = uuid.uuid4()
    user.email = "<EMAIL>"
    user.is_active = True
    return user


# Performance and stress tests
class TestFrameworkPerformance:
    """Performance tests for cybersecurity frameworks"""

    def test_bulk_import_performance(self, db: Session):
        """Test performance of bulk framework import"""
        service = CybersecurityFrameworksService(db)
        user_id = uuid.uuid4()

        # Create large framework data
        large_framework_data = {
            "name": "Large Performance Test Framework",
            "framework_type": "nist_csf_2_0",
            "version": "1.0",
            "description": "Large framework for performance testing",
            "authority": "Test Authority",
            "domains": []
        }

        # Generate 10 domains with 10 categories each, 5 controls per category
        for domain_idx in range(10):
            domain = {
                "domain_id": f"DOM{domain_idx:02d}",
                "name": f"Domain {domain_idx}",
                "description": f"Domain {domain_idx} description",
                "categories": []
            }

            for cat_idx in range(10):
                category = {
                    "category_id": f"DOM{domain_idx:02d}.CAT{cat_idx:02d}",
                    "name": f"Category {cat_idx}",
                    "description": f"Category {cat_idx} description",
                    "controls": []
                }

                for ctrl_idx in range(5):
                    control = {
                        "control_id": f"DOM{domain_idx:02d}.CAT{cat_idx:02d}-{ctrl_idx:02d}",
                        "name": f"Control {ctrl_idx}",
                        "description": f"Control {ctrl_idx} description",
                        "objective": f"Control {ctrl_idx} objective",
                        "control_type": "preventive",
                        "risk_rating": "medium",
                        "automation_level": "manual"
                    }
                    category["controls"].append(control)

                domain["categories"].append(category)

            large_framework_data["domains"].append(domain)

        # Test import performance (500 controls total)
        import time
        start_time = time.time()

        framework = service.import_framework_data(
            FrameworkType.NIST_CSF_2_0,
            large_framework_data,
            user_id
        )

        end_time = time.time()
        execution_time = end_time - start_time

        # Should complete within reasonable time (< 10 seconds for 500 controls)
        assert execution_time < 10.0
        assert len(framework.controls) == 500
        assert len(framework.domains) == 10

    def test_search_performance(self, db: Session):
        """Test search performance with large dataset"""
        # This test would be implemented with a pre-populated database
        # containing thousands of controls for realistic performance testing
        pass


# Edge case and error handling tests
class TestFrameworkEdgeCases:
    """Edge case tests for cybersecurity frameworks"""

    def test_duplicate_framework_handling(self, db: Session):
        """Test handling of duplicate framework creation"""
        service = CybersecurityFrameworksService(db)
        user_id = uuid.uuid4()

        framework_data = {
            "name": "Duplicate Test Framework",
            "framework_type": "nist_csf_2_0",
            "version": "1.0",
            "description": "Test duplicate handling",
            "authority": "Test Authority",
            "domains": []
        }

        # Create first framework
        framework1 = service.import_framework_data(
            FrameworkType.NIST_CSF_2_0,
            framework_data,
            user_id
        )

        # Attempt to create duplicate should raise error
        with pytest.raises(Exception):
            service.import_framework_data(
                FrameworkType.NIST_CSF_2_0,
                framework_data,
                user_id
            )

    def test_invalid_control_references(self, db: Session):
        """Test handling of invalid control references"""
        service = CybersecurityFrameworksService(db)
        user_id = uuid.uuid4()

        # Test bulk update with invalid control ID
        updates = [
            {
                "control_id": uuid.uuid4(),  # Non-existent control
                "status": "implemented",
                "responsible_party": "Test Team"
            }
        ]

        result = service.bulk_update_implementations(updates, user_id)

        # Should handle gracefully with error reporting
        assert result["created"] == 0
        assert result["updated"] == 0
        assert len(result["errors"]) == 1

    def test_malformed_framework_data(self, db: Session):
        """Test handling of malformed framework data"""
        service = CybersecurityFrameworksService(db)
        user_id = uuid.uuid4()

        # Test with missing required fields
        malformed_data = {
            "name": "Malformed Framework",
            # Missing framework_type and version
            "description": "Test malformed data handling"
        }

        with pytest.raises(Exception):
            service.import_framework_data(
                FrameworkType.NIST_CSF_2_0,
                malformed_data,
                user_id
            )
