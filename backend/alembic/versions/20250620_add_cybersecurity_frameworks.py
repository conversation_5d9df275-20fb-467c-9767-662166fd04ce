"""Add cybersecurity frameworks tables

Revision ID: 20250620_add_cybersecurity_frameworks
Revises: previous_revision
Create Date: 2025-06-20 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20250620_add_cybersecurity_frameworks'
down_revision = 'previous_revision'  # Replace with actual previous revision
branch_labels = None
depends_on = None


def upgrade():
    """Create cybersecurity frameworks tables"""
    
    # Create cybersecurity_frameworks table
    op.create_table(
        'cybersecurity_frameworks',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.text('gen_random_uuid()')),
        sa.Column('name', sa.String(255), nullable=False, index=True),
        sa.Column('framework_type', sa.String(50), nullable=False, index=True),
        sa.Column('version', sa.String(50), nullable=False),
        sa.Column('description', sa.Text),
        sa.Column('authority', sa.String(255)),
        sa.Column('publication_date', sa.DateTime),
        sa.Column('effective_date', sa.DateTime),
        sa.Column('status', sa.String(50), default='active'),
        sa.Column('metadata', postgresql.JSONB),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime, nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, nullable=False, default=sa.func.now()),
        sa.Column('created_by', postgresql.UUID(as_uuid=True)),
        sa.Column('updated_by', postgresql.UUID(as_uuid=True)),
        
        # Soft delete
        sa.Column('deleted_at', sa.DateTime),
        
        # Constraints
        sa.UniqueConstraint('name', 'version', name='uq_framework_name_version'),
        sa.Index('idx_framework_type_status', 'framework_type', 'status'),
    )
    
    # Create framework_domains table
    op.create_table(
        'framework_domains',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.text('gen_random_uuid()')),
        sa.Column('framework_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('domain_id', sa.String(100), nullable=False),
        sa.Column('name', sa.String(500), nullable=False),
        sa.Column('description', sa.Text),
        sa.Column('sort_order', sa.Integer, default=0),
        sa.Column('metadata', postgresql.JSONB),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime, nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, nullable=False, default=sa.func.now()),
        sa.Column('created_by', postgresql.UUID(as_uuid=True)),
        sa.Column('updated_by', postgresql.UUID(as_uuid=True)),
        
        # Soft delete
        sa.Column('deleted_at', sa.DateTime),
        
        # Foreign keys
        sa.ForeignKeyConstraint(['framework_id'], ['cybersecurity_frameworks.id']),
        
        # Constraints
        sa.UniqueConstraint('framework_id', 'domain_id', name='uq_domain_framework_id'),
        sa.Index('idx_domain_framework', 'framework_id', 'sort_order'),
    )
    
    # Create framework_categories table
    op.create_table(
        'framework_categories',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.text('gen_random_uuid()')),
        sa.Column('domain_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('category_id', sa.String(100), nullable=False),
        sa.Column('name', sa.String(500), nullable=False),
        sa.Column('description', sa.Text),
        sa.Column('sort_order', sa.Integer, default=0),
        sa.Column('parent_category_id', postgresql.UUID(as_uuid=True)),
        sa.Column('metadata', postgresql.JSONB),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime, nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, nullable=False, default=sa.func.now()),
        sa.Column('created_by', postgresql.UUID(as_uuid=True)),
        sa.Column('updated_by', postgresql.UUID(as_uuid=True)),
        
        # Soft delete
        sa.Column('deleted_at', sa.DateTime),
        
        # Foreign keys
        sa.ForeignKeyConstraint(['domain_id'], ['framework_domains.id']),
        sa.ForeignKeyConstraint(['parent_category_id'], ['framework_categories.id']),
        
        # Constraints
        sa.UniqueConstraint('domain_id', 'category_id', name='uq_category_domain_id'),
        sa.Index('idx_category_domain', 'domain_id', 'sort_order'),
    )
    
    # Create framework_controls table
    op.create_table(
        'framework_controls',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.text('gen_random_uuid()')),
        sa.Column('framework_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('domain_id', postgresql.UUID(as_uuid=True)),
        sa.Column('category_id', postgresql.UUID(as_uuid=True)),
        sa.Column('control_id', sa.String(100), nullable=False),
        sa.Column('name', sa.String(500), nullable=False),
        sa.Column('description', sa.Text),
        sa.Column('objective', sa.Text),
        sa.Column('implementation_guidance', sa.Text),
        sa.Column('control_type', sa.String(50)),
        sa.Column('automation_level', sa.String(50)),
        sa.Column('risk_rating', sa.String(50)),
        sa.Column('priority', sa.String(50)),
        sa.Column('assessment_criteria', postgresql.JSONB),
        sa.Column('testing_procedures', postgresql.JSONB),
        sa.Column('evidence_requirements', postgresql.JSONB),
        sa.Column('parent_control_id', postgresql.UUID(as_uuid=True)),
        sa.Column('sort_order', sa.Integer, default=0),
        sa.Column('references', postgresql.JSONB),
        sa.Column('tags', postgresql.JSONB),
        sa.Column('metadata', postgresql.JSONB),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime, nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, nullable=False, default=sa.func.now()),
        sa.Column('created_by', postgresql.UUID(as_uuid=True)),
        sa.Column('updated_by', postgresql.UUID(as_uuid=True)),
        
        # Soft delete
        sa.Column('deleted_at', sa.DateTime),
        
        # Foreign keys
        sa.ForeignKeyConstraint(['framework_id'], ['cybersecurity_frameworks.id']),
        sa.ForeignKeyConstraint(['domain_id'], ['framework_domains.id']),
        sa.ForeignKeyConstraint(['category_id'], ['framework_categories.id']),
        sa.ForeignKeyConstraint(['parent_control_id'], ['framework_controls.id']),
        
        # Constraints
        sa.UniqueConstraint('framework_id', 'control_id', name='uq_control_framework_id'),
        sa.Index('idx_control_framework', 'framework_id', 'sort_order'),
        sa.Index('idx_control_type_risk', 'control_type', 'risk_rating'),
        sa.Index('idx_control_search', 'name', 'description'),
    )
    
    # Create control_implementations table
    op.create_table(
        'control_implementations',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.text('gen_random_uuid()')),
        sa.Column('control_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('organization_id', postgresql.UUID(as_uuid=True)),
        sa.Column('status', sa.String(50), nullable=False, default='not_implemented'),
        sa.Column('implementation_date', sa.DateTime),
        sa.Column('target_completion_date', sa.DateTime),
        sa.Column('responsible_party', sa.String(255)),
        sa.Column('evidence', postgresql.JSONB),
        sa.Column('documentation', postgresql.JSONB),
        sa.Column('automated_checks', postgresql.JSONB),
        sa.Column('gaps', postgresql.JSONB),
        sa.Column('remediation_plan', sa.Text),
        sa.Column('remediation_priority', sa.String(50)),
        sa.Column('implementation_cost', sa.Float),
        sa.Column('effort_estimate', sa.Float),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime, nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, nullable=False, default=sa.func.now()),
        sa.Column('created_by', postgresql.UUID(as_uuid=True)),
        sa.Column('updated_by', postgresql.UUID(as_uuid=True)),
        
        # Soft delete
        sa.Column('deleted_at', sa.DateTime),
        
        # Foreign keys
        sa.ForeignKeyConstraint(['control_id'], ['framework_controls.id']),
        
        # Indexes
        sa.Index('idx_implementation_status', 'status', 'target_completion_date'),
        sa.Index('idx_implementation_org', 'organization_id', 'status'),
    )
    
    # Create control_assessments table
    op.create_table(
        'control_assessments',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.text('gen_random_uuid()')),
        sa.Column('control_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('implementation_id', postgresql.UUID(as_uuid=True)),
        sa.Column('assessment_date', sa.DateTime, nullable=False, default=sa.func.now()),
        sa.Column('assessor', sa.String(255), nullable=False),
        sa.Column('assessment_type', sa.String(50)),
        sa.Column('status', sa.String(50), nullable=False),
        sa.Column('effectiveness_rating', sa.String(50)),
        sa.Column('maturity_level', sa.Integer),
        sa.Column('confidence_level', sa.String(50)),
        sa.Column('findings', postgresql.JSONB),
        sa.Column('recommendations', postgresql.JSONB),
        sa.Column('evidence_reviewed', postgresql.JSONB),
        sa.Column('compliance_score', sa.Float),
        sa.Column('risk_score', sa.Float),
        sa.Column('next_assessment_date', sa.DateTime),
        sa.Column('assessment_frequency', sa.String(50)),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime, nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, nullable=False, default=sa.func.now()),
        sa.Column('created_by', postgresql.UUID(as_uuid=True)),
        sa.Column('updated_by', postgresql.UUID(as_uuid=True)),
        
        # Soft delete
        sa.Column('deleted_at', sa.DateTime),
        
        # Foreign keys
        sa.ForeignKeyConstraint(['control_id'], ['framework_controls.id']),
        sa.ForeignKeyConstraint(['implementation_id'], ['control_implementations.id']),
        
        # Indexes
        sa.Index('idx_assessment_date_status', 'assessment_date', 'status'),
        sa.Index('idx_assessment_control', 'control_id', 'assessment_date'),
        
        # Constraints
        sa.CheckConstraint('maturity_level >= 1 AND maturity_level <= 5', name='chk_maturity_level_range'),
        sa.CheckConstraint('compliance_score >= 0 AND compliance_score <= 100', name='chk_compliance_score_range'),
    )


def downgrade():
    """Drop cybersecurity frameworks tables"""
    
    # Drop tables in reverse order due to foreign key constraints
    op.drop_table('control_assessments')
    op.drop_table('control_implementations')
    op.drop_table('framework_controls')
    op.drop_table('framework_categories')
    op.drop_table('framework_domains')
    op.drop_table('cybersecurity_frameworks')
