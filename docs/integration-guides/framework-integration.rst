Framework Integration Guide
===========================

.. contents:: Table of Contents
   :local:
   :depth: 3

Overview
--------

This guide provides comprehensive integration patterns and examples for connecting cybersecurity frameworks with existing Blast-Radius features and external systems. Learn how to leverage framework data for enhanced security operations and compliance automation.

Integration Architecture
------------------------

The cybersecurity frameworks module integrates with multiple Blast-Radius components:

.. mermaid::

   graph TB
       A[Cybersecurity Frameworks] --> B[MITRE ATT&CK]
       A --> C[Asset Discovery]
       A --> D[Attack Path Analysis]
       A --> E[Real-time Monitoring]
       A --> F[Threat Modeling]

       B --> G[Control-Technique Mapping]
       C --> H[Asset-Control Relationships]
       D --> I[Risk-based Prioritization]
       E --> J[Compliance Dashboards]
       F --> K[Framework-based Threat Models]

Core Integration Points
~~~~~~~~~~~~~~~~~~~~~~

1. **MITRE ATT&CK Integration**
   - Map framework controls to MITRE techniques
   - Enrich controls with threat intelligence
   - Correlate attack paths with control gaps

2. **Asset Management Integration**
   - Link controls to specific assets
   - Track control implementation per asset
   - Asset-based compliance reporting

3. **Real-time Monitoring Integration**
   - Compliance status dashboards
   - Real-time control validation
   - Automated compliance alerting

4. **Attack Path Analysis Integration**
   - Risk-based control prioritization
   - Control effectiveness in attack scenarios
   - Blast radius impact assessment

MITRE ATT&CK Integration
-----------------------

Control-Technique Mapping
~~~~~~~~~~~~~~~~~~~~~~~~~

Map cybersecurity framework controls to MITRE ATT&CK techniques for enhanced threat context.

**Implementation Example:**

.. code-block:: python

   from app.services.cybersecurity_frameworks_service import CybersecurityFrameworksService
   from app.services.mitre_service import MitreService

   async def map_controls_to_techniques(framework_id: UUID, db: Session):
       """Map framework controls to MITRE techniques"""

       frameworks_service = CybersecurityFrameworksService(db)
       mitre_service = MitreService(db)

       # Get all controls for the framework
       controls = await frameworks_service.get_framework_controls(framework_id)

       mappings = []
       for control in controls:
           # Find relevant MITRE techniques
           techniques = await mitre_service.search_techniques(
               query=f"{control.name} {control.description}",
               limit=5
           )

           # Create mappings based on relevance
           for technique in techniques:
               if calculate_relevance(control, technique) > 0.7:
                   mappings.append({
                       "control_id": control.id,
                       "technique_id": technique.id,
                       "relevance_score": calculate_relevance(control, technique),
                       "mapping_type": "preventive" if control.control_type == "preventive" else "detective"
                   })

       return mappings

   def calculate_relevance(control, technique):
       """Calculate relevance score between control and technique"""
       # Implement similarity algorithm
       # Consider: keywords, tactics, control type, etc.
       pass

**API Integration:**

.. code-block:: http

   POST /api/v1/frameworks/controls/{control_id}/mitre-mappings
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "technique_id": "T1078",
     "relevance_score": 0.85,
     "mapping_type": "preventive",
     "rationale": "Control addresses valid account usage"
   }

Threat Intelligence Enrichment
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Enrich framework controls with MITRE threat intelligence data.

.. code-block:: python

   async def enrich_control_with_threat_intel(control_id: UUID, db: Session):
       """Enrich control with threat intelligence"""

       # Get control and its MITRE mappings
       control = await get_control(control_id, db)
       mappings = await get_control_mitre_mappings(control_id, db)

       threat_intel = {
           "related_techniques": [],
           "threat_actors": [],
           "attack_patterns": [],
           "risk_assessment": {}
       }

       for mapping in mappings:
           technique = await get_mitre_technique(mapping.technique_id, db)

           # Add technique details
           threat_intel["related_techniques"].append({
               "technique_id": technique.technique_id,
               "name": technique.name,
               "tactic": technique.tactic,
               "relevance": mapping.relevance_score
           })

           # Get associated threat actors
           actors = await get_technique_threat_actors(technique.id, db)
           threat_intel["threat_actors"].extend(actors)

       # Calculate risk assessment
       threat_intel["risk_assessment"] = calculate_threat_risk(
           control, threat_intel["related_techniques"]
       )

       return threat_intel

Asset Management Integration
---------------------------

Asset-Control Relationships
~~~~~~~~~~~~~~~~~~~~~~~~~~

Link cybersecurity controls to specific assets for granular compliance tracking.

**Database Schema Extension:**

.. code-block:: sql

   CREATE TABLE asset_control_mappings (
       id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
       asset_id UUID NOT NULL REFERENCES assets(id),
       control_id UUID NOT NULL REFERENCES framework_controls(id),
       applicability_score FLOAT DEFAULT 1.0,
       implementation_status VARCHAR(50) DEFAULT 'not_assessed',
       last_assessment_date TIMESTAMP,
       next_assessment_date TIMESTAMP,
       created_at TIMESTAMP DEFAULT NOW(),
       updated_at TIMESTAMP DEFAULT NOW(),
       UNIQUE(asset_id, control_id)
   );

**Implementation Service:**

.. code-block:: python

   class AssetControlMappingService:
       """Service for managing asset-control relationships"""

       def __init__(self, db: Session):
           self.db = db

       async def map_controls_to_asset(
           self,
           asset_id: UUID,
           framework_ids: List[UUID],
           auto_assess: bool = True
       ) -> List[AssetControlMapping]:
           """Map applicable controls to an asset"""

           mappings = []

           for framework_id in framework_ids:
               controls = await self.get_framework_controls(framework_id)

               for control in controls:
                   # Determine applicability
                   applicability = await self.calculate_control_applicability(
                       asset_id, control.id
                   )

                   if applicability > 0.5:  # Threshold for applicability
                       mapping = AssetControlMapping(
                           asset_id=asset_id,
                           control_id=control.id,
                           applicability_score=applicability,
                           implementation_status='not_assessed'
                       )

                       if auto_assess:
                           # Perform automated assessment
                           status = await self.auto_assess_control(asset_id, control.id)
                           mapping.implementation_status = status
                           mapping.last_assessment_date = datetime.utcnow()

                       mappings.append(mapping)
                       self.db.add(mapping)

           self.db.commit()
           return mappings

       async def calculate_control_applicability(
           self,
           asset_id: UUID,
           control_id: UUID
       ) -> float:
           """Calculate how applicable a control is to an asset"""

           asset = await self.get_asset(asset_id)
           control = await self.get_control(control_id)

           # Factors affecting applicability
           factors = {
               'asset_type': self.match_asset_type(asset, control),
               'technology_stack': self.match_technology(asset, control),
               'risk_level': self.match_risk_level(asset, control),
               'compliance_scope': self.match_compliance_scope(asset, control)
           }

           # Weighted calculation
           weights = {'asset_type': 0.3, 'technology_stack': 0.3, 'risk_level': 0.2, 'compliance_scope': 0.2}

           applicability = sum(
               factors[factor] * weights[factor]
               for factor in factors
           )

           return min(applicability, 1.0)

**Asset Compliance Dashboard:**

.. code-block:: python

   async def get_asset_compliance_status(asset_id: UUID, db: Session):
       """Get compliance status for a specific asset"""

       mappings = db.query(AssetControlMapping).filter(
           AssetControlMapping.asset_id == asset_id
       ).all()

       compliance_status = {
           'asset_id': str(asset_id),
           'total_controls': len(mappings),
           'implemented': 0,
           'partially_implemented': 0,
           'not_implemented': 0,
           'not_assessed': 0,
           'compliance_percentage': 0.0,
           'framework_breakdown': {},
           'high_priority_gaps': []
       }

       for mapping in mappings:
           status = mapping.implementation_status
           compliance_status[status] += 1

           # Framework breakdown
           framework_name = mapping.control.framework.name
           if framework_name not in compliance_status['framework_breakdown']:
               compliance_status['framework_breakdown'][framework_name] = {
                   'total': 0, 'implemented': 0, 'compliance_percentage': 0.0
               }

           compliance_status['framework_breakdown'][framework_name]['total'] += 1
           if status == 'implemented':
               compliance_status['framework_breakdown'][framework_name]['implemented'] += 1

           # High priority gaps
           if (status in ['not_implemented', 'not_assessed'] and
               mapping.control.risk_rating in ['high', 'critical']):
               compliance_status['high_priority_gaps'].append({
                   'control_id': mapping.control.control_id,
                   'control_name': mapping.control.name,
                   'framework': framework_name,
                   'risk_rating': mapping.control.risk_rating
               })

       # Calculate percentages
       implemented_count = compliance_status['implemented']
       total_count = compliance_status['total_controls']

       if total_count > 0:
           compliance_status['compliance_percentage'] = (implemented_count / total_count) * 100

       for framework_data in compliance_status['framework_breakdown'].values():
           if framework_data['total'] > 0:
               framework_data['compliance_percentage'] = (
                   framework_data['implemented'] / framework_data['total']
               ) * 100

       return compliance_status

Real-time Monitoring Integration
-------------------------------

Compliance Dashboards
~~~~~~~~~~~~~~~~~~~~

Create real-time compliance monitoring dashboards using WebSocket integration.

**WebSocket Event Handler:**

.. code-block:: python

   from app.api.v1.realtime import websocket_manager

   class ComplianceMonitoringService:
       """Real-time compliance monitoring service"""

       def __init__(self, db: Session):
           self.db = db

       async def start_compliance_monitoring(self, user_id: UUID):
           """Start real-time compliance monitoring"""

           while True:
               try:
                   # Get current compliance status
                   compliance_data = await self.get_real_time_compliance_data()

                   # Send updates via WebSocket
                   await websocket_manager.send_personal_message(
                       user_id,
                       {
                           "type": "compliance_update",
                           "data": compliance_data,
                           "timestamp": datetime.utcnow().isoformat()
                       }
                   )

                   # Wait before next update
                   await asyncio.sleep(30)  # Update every 30 seconds

               except Exception as e:
                   logger.error(f"Compliance monitoring error: {e}")
                   await asyncio.sleep(60)  # Wait longer on error

       async def get_real_time_compliance_data(self):
           """Get current compliance data for dashboard"""

           # Get framework compliance summary
           frameworks_summary = await self.get_frameworks_compliance_summary()

           # Get recent assessment activities
           recent_assessments = await self.get_recent_assessments(limit=10)

           # Get compliance trends
           trends = await self.get_compliance_trends(days=30)

           # Get critical gaps
           critical_gaps = await self.get_critical_compliance_gaps()

           return {
               "frameworks_summary": frameworks_summary,
               "recent_assessments": recent_assessments,
               "trends": trends,
               "critical_gaps": critical_gaps,
               "last_updated": datetime.utcnow().isoformat()
           }

**Frontend Dashboard Component:**

.. code-block:: javascript

   import { useWebSocket } from '@/hooks/useWebSocket';
   import { ComplianceChart } from '@/components/charts/ComplianceChart';

   export function ComplianceDashboard() {
     const [complianceData, setComplianceData] = useState(null);

     const { lastMessage } = useWebSocket('/api/v1/realtime/ws', {
       onMessage: (message) => {
         const data = JSON.parse(message.data);
         if (data.type === 'compliance_update') {
           setComplianceData(data.data);
         }
       }
     });

     return (
       <div className="compliance-dashboard">
         <div className="dashboard-header">
           <h2>Real-time Compliance Monitoring</h2>
           <span className="last-updated">
             Last Updated: {complianceData?.last_updated}
           </span>
         </div>

         <div className="dashboard-grid">
           <ComplianceChart data={complianceData?.frameworks_summary} />
           <RecentAssessments assessments={complianceData?.recent_assessments} />
           <ComplianceTrends trends={complianceData?.trends} />
           <CriticalGaps gaps={complianceData?.critical_gaps} />
         </div>
       </div>
     );
   }

Automated Compliance Validation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Implement automated control validation using real-time monitoring data.

.. code-block:: python

   class AutomatedComplianceValidator:
       """Automated compliance validation service"""

       def __init__(self, db: Session):
           self.db = db
           self.validators = {
               'access_control': self.validate_access_controls,
               'encryption': self.validate_encryption_controls,
               'monitoring': self.validate_monitoring_controls,
               'backup': self.validate_backup_controls
           }

       async def validate_control_implementation(
           self,
           control_id: UUID,
           asset_id: UUID = None
       ) -> Dict[str, Any]:
           """Validate control implementation automatically"""

           control = await self.get_control(control_id)
           validation_result = {
               'control_id': str(control_id),
               'validation_date': datetime.utcnow().isoformat(),
               'status': 'unknown',
               'compliance_score': 0.0,
               'findings': [],
               'evidence': [],
               'recommendations': []
           }

           # Determine validation method based on control type
           validator_type = self.determine_validator_type(control)

           if validator_type in self.validators:
               validator = self.validators[validator_type]
               result = await validator(control, asset_id)
               validation_result.update(result)
           else:
               validation_result['status'] = 'manual_review_required'
               validation_result['findings'].append(
                   "Automated validation not available for this control type"
               )

           # Store validation result
           await self.store_validation_result(validation_result)

           return validation_result

       async def validate_access_controls(self, control: FrameworkControl, asset_id: UUID):
           """Validate access control implementations"""

           findings = []
           evidence = []
           compliance_score = 0.0

           if asset_id:
               # Asset-specific validation
               asset = await self.get_asset(asset_id)

               # Check for access control configurations
               if hasattr(asset, 'access_policies'):
                   evidence.append({
                       'type': 'configuration',
                       'description': 'Access policies configured',
                       'details': asset.access_policies
                   })
                   compliance_score += 30

               # Check for authentication mechanisms
               if hasattr(asset, 'auth_mechanisms'):
                   evidence.append({
                       'type': 'configuration',
                       'description': 'Authentication mechanisms in place',
                       'details': asset.auth_mechanisms
                   })
                   compliance_score += 40

               # Check for access logging
               if hasattr(asset, 'access_logs') and asset.access_logs:
                   evidence.append({
                       'type': 'logs',
                       'description': 'Access logging enabled',
                       'details': 'Recent access events found'
                   })
                   compliance_score += 30
               else:
                   findings.append({
                       'severity': 'medium',
                       'description': 'Access logging not detected',
                       'recommendation': 'Enable comprehensive access logging'
                   })

           status = 'implemented' if compliance_score >= 80 else 'partially_implemented'

           return {
               'status': status,
               'compliance_score': min(compliance_score, 100.0),
               'findings': findings,
               'evidence': evidence,
               'recommendations': [
                   'Review access control policies regularly',
                   'Implement multi-factor authentication where applicable'
               ]
           }

Attack Path Analysis Integration
-------------------------------

Risk-based Control Prioritization
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Integrate framework controls with attack path analysis for risk-based prioritization.

.. code-block:: python

   from app.services.attack_path_service import AttackPathService

   class RiskBasedControlPrioritization:
       """Risk-based control prioritization using attack path analysis"""

       def __init__(self, db: Session):
           self.db = db
           self.attack_path_service = AttackPathService(db)

       async def prioritize_controls_by_attack_paths(
           self,
           framework_id: UUID,
           target_assets: List[UUID] = None
       ) -> List[Dict[str, Any]]:
           """Prioritize controls based on attack path analysis"""

           # Get all controls for the framework
           controls = await self.get_framework_controls(framework_id)

           # Get attack paths
           attack_paths = await self.attack_path_service.get_attack_paths(
               target_assets=target_assets,
               max_depth=5
           )

           control_priorities = []

           for control in controls:
               priority_score = await self.calculate_control_priority(
                   control, attack_paths
               )

               control_priorities.append({
                   'control_id': str(control.id),
                   'control_name': control.name,
                   'framework_control_id': control.control_id,
                   'priority_score': priority_score,
                   'risk_rating': control.risk_rating,
                   'attack_path_relevance': priority_score['attack_path_relevance'],
                   'blast_radius_impact': priority_score['blast_radius_impact'],
                   'implementation_urgency': priority_score['implementation_urgency']
               })

           # Sort by priority score
           control_priorities.sort(
               key=lambda x: x['priority_score']['total_score'],
               reverse=True
           )

           return control_priorities

       async def calculate_control_priority(
           self,
           control: FrameworkControl,
           attack_paths: List[Dict]
       ) -> Dict[str, float]:
           """Calculate priority score for a control"""

           scores = {
               'attack_path_relevance': 0.0,
               'blast_radius_impact': 0.0,
               'implementation_urgency': 0.0,
               'total_score': 0.0
           }

           # Attack path relevance
           relevant_paths = 0
           total_path_criticality = 0.0

           for path in attack_paths:
               if self.control_affects_attack_path(control, path):
                   relevant_paths += 1
                   total_path_criticality += path.get('criticality_score', 0.5)

           if relevant_paths > 0:
               scores['attack_path_relevance'] = min(
                   (relevant_paths * total_path_criticality) / len(attack_paths),
                   1.0
               )

           # Blast radius impact
           if control.risk_rating == 'critical':
               scores['blast_radius_impact'] = 1.0
           elif control.risk_rating == 'high':
               scores['blast_radius_impact'] = 0.8
           elif control.risk_rating == 'medium':
               scores['blast_radius_impact'] = 0.5
           else:
               scores['blast_radius_impact'] = 0.2

           # Implementation urgency
           implementation = await self.get_latest_implementation(control.id)
           if not implementation or implementation.status == 'not_implemented':
               scores['implementation_urgency'] = 1.0
           elif implementation.status == 'partially_implemented':
               scores['implementation_urgency'] = 0.7
           else:
               scores['implementation_urgency'] = 0.1

           # Calculate total score (weighted)
           weights = {
               'attack_path_relevance': 0.4,
               'blast_radius_impact': 0.3,
               'implementation_urgency': 0.3
           }

           scores['total_score'] = sum(
               scores[metric] * weights[metric]
               for metric in weights
           )

           return scores

External System Integration
--------------------------

SIEM Integration
~~~~~~~~~~~~~~~

Integrate framework compliance data with SIEM systems for enhanced security monitoring.

.. code-block:: python

   class SIEMIntegrationService:
       """SIEM integration for compliance data"""

       def __init__(self, siem_config: Dict[str, Any]):
           self.siem_config = siem_config
           self.siem_client = self.create_siem_client()

       async def send_compliance_events(self, events: List[Dict[str, Any]]):
           """Send compliance events to SIEM"""

           for event in events:
               siem_event = self.format_compliance_event(event)
               await self.siem_client.send_event(siem_event)

       def format_compliance_event(self, event: Dict[str, Any]) -> Dict[str, Any]:
           """Format compliance event for SIEM consumption"""

           return {
               'timestamp': event.get('timestamp', datetime.utcnow().isoformat()),
               'event_type': 'compliance_assessment',
               'source': 'blast-radius-frameworks',
               'severity': self.map_compliance_severity(event.get('status')),
               'framework': event.get('framework_name'),
               'control_id': event.get('control_id'),
               'control_name': event.get('control_name'),
               'assessment_status': event.get('status'),
               'compliance_score': event.get('compliance_score'),
               'assessor': event.get('assessor'),
               'findings': event.get('findings', []),
               'asset_id': event.get('asset_id'),
               'organization_id': event.get('organization_id'),
               'custom_fields': {
                   'framework_type': event.get('framework_type'),
                   'risk_rating': event.get('risk_rating'),
                   'automation_level': event.get('automation_level')
               }
           }

ServiceNow Integration
~~~~~~~~~~~~~~~~~~~~

Integrate with ServiceNow for automated ticket creation and compliance tracking.

.. code-block:: python

   class ServiceNowIntegration:
       """ServiceNow integration for compliance management"""

       def __init__(self, servicenow_config: Dict[str, Any]):
           self.config = servicenow_config
           self.client = ServiceNowClient(servicenow_config)

       async def create_compliance_incident(
           self,
           control_gap: Dict[str, Any]
       ) -> str:
           """Create ServiceNow incident for compliance gap"""

           incident_data = {
               'short_description': f"Compliance Gap: {control_gap['control_name']}",
               'description': self.format_gap_description(control_gap),
               'category': 'Security',
               'subcategory': 'Compliance',
               'priority': self.map_priority(control_gap['risk_rating']),
               'assignment_group': self.config.get('security_team_group'),
               'custom_fields': {
                   'u_framework': control_gap['framework_name'],
                   'u_control_id': control_gap['control_id'],
                   'u_compliance_score': control_gap.get('compliance_score', 0),
                   'u_blast_radius_id': control_gap.get('blast_radius_control_id')
               }
           }

           incident_id = await self.client.create_incident(incident_data)

           # Update control implementation with ServiceNow reference
           await self.update_control_with_servicenow_ref(
               control_gap['control_id'],
               incident_id
           )

           return incident_id

Best Practices
--------------

Integration Patterns
~~~~~~~~~~~~~~~~~~~

1. **Event-Driven Architecture**
   - Use events for real-time updates
   - Implement proper error handling
   - Ensure idempotent operations

2. **Data Consistency**
   - Maintain referential integrity
   - Implement proper transaction handling
   - Use database constraints

3. **Performance Optimization**
   - Implement caching strategies
   - Use async operations where possible
   - Optimize database queries

4. **Security Considerations**
   - Validate all inputs
   - Implement proper authentication
   - Use encrypted communications

Monitoring and Alerting
~~~~~~~~~~~~~~~~~~~~~~

1. **Integration Health Monitoring**
   - Monitor API response times
   - Track integration success rates
   - Alert on integration failures

2. **Data Quality Monitoring**
   - Validate data consistency
   - Monitor for data drift
   - Implement data quality metrics

3. **Compliance Alerting**
   - Real-time compliance gap alerts
   - Threshold-based notifications
   - Escalation procedures

Troubleshooting
---------------

Common Integration Issues
~~~~~~~~~~~~~~~~~~~~~~~~

1. **API Rate Limiting**
   - Implement exponential backoff
   - Use connection pooling
   - Monitor rate limit headers

2. **Data Synchronization**
   - Handle eventual consistency
   - Implement conflict resolution
   - Use proper timestamps

3. **Performance Issues**
   - Optimize database queries
   - Implement proper indexing
   - Use caching strategies

Next Steps
----------

* :doc:`../user-guides/cybersecurity-frameworks` - Framework management guide
* :doc:`../api-reference/cybersecurity-frameworks-api` - Complete API reference
* :doc:`../compliance-workflows/assessment-procedures` - Assessment workflows