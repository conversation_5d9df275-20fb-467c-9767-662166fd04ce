Cybersecurity Frameworks API Reference
======================================

.. contents:: Table of Contents
   :local:
   :depth: 3

Overview
--------

The Cybersecurity Frameworks API provides comprehensive endpoints for managing cybersecurity frameworks, controls, implementations, and assessments. This API supports ISF 2022, NIST CSF 2.0, ISO/IEC 27001:2022, and CIS Controls v8.

Base URL: ``/api/v1/frameworks``

Authentication
--------------

All API endpoints require authentication using Bearer tokens:

.. code-block:: http

   Authorization: Bearer <your-access-token>

Framework Management
-------------------

List Frameworks
~~~~~~~~~~~~~~

.. http:get:: /frameworks

   Retrieve a list of all cybersecurity frameworks with optional filtering.

   **Query Parameters:**

   * ``framework_type`` (string, optional) - Filter by framework type (isf_2022, nist_csf_2_0, iso_27001_2022, cis_controls_v8)
   * ``status`` (string, optional) - Filter by status (active, inactive, draft)
   * ``skip`` (integer, optional) - Number of records to skip (default: 0)
   * ``limit`` (integer, optional) - Maximum number of records to return (default: 100, max: 1000)

   **Example Request:**

   .. code-block:: http

      GET /api/v1/frameworks/frameworks?framework_type=nist_csf_2_0&limit=50
      Authorization: Bearer <token>

   **Example Response:**

   .. code-block:: json

      [
        {
          "id": "123e4567-e89b-12d3-a456-************",
          "name": "NIST Cybersecurity Framework 2.0",
          "framework_type": "nist_csf_2_0",
          "version": "2.0",
          "description": "A voluntary framework...",
          "authority": "NIST",
          "publication_date": "2024-02-26T00:00:00Z",
          "effective_date": "2024-02-26T00:00:00Z",
          "status": "active",
          "created_at": "2024-01-01T00:00:00Z",
          "updated_at": "2024-01-01T00:00:00Z"
        }
      ]

Create Framework
~~~~~~~~~~~~~~~

.. http:post:: /frameworks

   Create a new cybersecurity framework.

   **Request Body:**

   .. code-block:: json

      {
        "name": "Custom Framework",
        "framework_type": "nist_csf_2_0",
        "version": "1.0",
        "description": "Custom framework description",
        "authority": "Organization Name",
        "publication_date": "2024-01-01T00:00:00Z",
        "effective_date": "2024-01-01T00:00:00Z",
        "metadata": {
          "custom_field": "value"
        }
      }

   **Response:** Returns the created framework object with HTTP 201 status.

Get Framework
~~~~~~~~~~~~

.. http:get:: /frameworks/{framework_id}

   Retrieve a specific framework by ID.

   **Path Parameters:**

   * ``framework_id`` (UUID) - Framework identifier

   **Response:** Returns the framework object or HTTP 404 if not found.

Update Framework
~~~~~~~~~~~~~~~

.. http:put:: /frameworks/{framework_id}

   Update an existing framework.

   **Path Parameters:**

   * ``framework_id`` (UUID) - Framework identifier

   **Request Body:** Partial framework object with fields to update.

   **Response:** Returns the updated framework object.

Delete Framework
~~~~~~~~~~~~~~~

.. http:delete:: /frameworks/{framework_id}

   Soft delete a framework (sets deleted_at timestamp).

   **Path Parameters:**

   * ``framework_id`` (UUID) - Framework identifier

   **Response:** HTTP 204 No Content on success.

Domain Management
----------------

List Framework Domains
~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /frameworks/{framework_id}/domains

   Retrieve all domains for a specific framework.

   **Path Parameters:**

   * ``framework_id`` (UUID) - Framework identifier

   **Example Response:**

   .. code-block:: json

      [
        {
          "id": "123e4567-e89b-12d3-a456-************",
          "framework_id": "123e4567-e89b-12d3-a456-************",
          "domain_id": "GV",
          "name": "Govern",
          "description": "The organization's cybersecurity risk management strategy...",
          "sort_order": 1,
          "created_at": "2024-01-01T00:00:00Z",
          "updated_at": "2024-01-01T00:00:00Z"
        }
      ]

Create Framework Domain
~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /frameworks/{framework_id}/domains

   Create a new domain for a framework.

   **Path Parameters:**

   * ``framework_id`` (UUID) - Framework identifier

   **Request Body:**

   .. code-block:: json

      {
        "domain_id": "ID",
        "name": "Identify",
        "description": "Understanding cybersecurity posture",
        "sort_order": 2,
        "metadata": {
          "color": "#blue"
        }
      }

Control Management
-----------------

List Framework Controls
~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /frameworks/{framework_id}/controls

   Retrieve all controls for a specific framework with optional filtering.

   **Path Parameters:**

   * ``framework_id`` (UUID) - Framework identifier

   **Query Parameters:**

   * ``domain_id`` (string, optional) - Filter by domain ID
   * ``category_id`` (string, optional) - Filter by category ID
   * ``control_type`` (string, optional) - Filter by control type
   * ``risk_rating`` (string, optional) - Filter by risk rating
   * ``skip`` (integer, optional) - Number of records to skip
   * ``limit`` (integer, optional) - Maximum number of records to return

   **Example Response:**

   .. code-block:: json

      [
        {
          "id": "123e4567-e89b-12d3-a456-************",
          "framework_id": "123e4567-e89b-12d3-a456-************",
          "domain_id": "123e4567-e89b-12d3-a456-************",
          "control_id": "GV.OC-01",
          "name": "Organizational Mission and Business Objectives",
          "description": "The organizational mission, objectives...",
          "objective": "Establish clear understanding...",
          "control_type": "governance",
          "automation_level": "manual",
          "risk_rating": "medium",
          "priority": "high",
          "tags": ["governance", "mission"],
          "created_at": "2024-01-01T00:00:00Z",
          "updated_at": "2024-01-01T00:00:00Z"
        }
      ]

Create Framework Control
~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /frameworks/{framework_id}/controls

   Create a new control for a framework.

   **Request Body:**

   .. code-block:: json

      {
        "control_id": "GV.OC-02",
        "name": "Control Name",
        "description": "Control description",
        "objective": "Control objective",
        "implementation_guidance": "Implementation guidance",
        "control_type": "governance",
        "automation_level": "manual",
        "risk_rating": "high",
        "priority": "critical",
        "assessment_criteria": {
          "criteria": ["Criterion 1", "Criterion 2"]
        },
        "testing_procedures": {
          "procedures": ["Test 1", "Test 2"]
        },
        "evidence_requirements": {
          "evidence": ["Document 1", "Document 2"]
        },
        "tags": ["governance", "policy"]
      }

Implementation Tracking
----------------------

List Control Implementations
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /controls/{control_id}/implementations

   Retrieve all implementations for a specific control.

   **Path Parameters:**

   * ``control_id`` (UUID) - Control identifier

   **Query Parameters:**

   * ``organization_id`` (UUID, optional) - Filter by organization
   * ``status`` (string, optional) - Filter by implementation status

   **Example Response:**

   .. code-block:: json

      [
        {
          "id": "123e4567-e89b-12d3-a456-************",
          "control_id": "123e4567-e89b-12d3-a456-************",
          "organization_id": "123e4567-e89b-12d3-a456-************",
          "status": "implemented",
          "implementation_date": "2024-01-15T00:00:00Z",
          "target_completion_date": "2024-02-01T00:00:00Z",
          "responsible_party": "Security Team",
          "evidence": [
            {
              "type": "document",
              "title": "Policy Document",
              "url": "https://example.com/policy.pdf"
            }
          ],
          "gaps": [],
          "remediation_plan": null,
          "implementation_cost": 5000.0,
          "effort_estimate": 40.0,
          "created_at": "2024-01-01T00:00:00Z",
          "updated_at": "2024-01-15T00:00:00Z"
        }
      ]

Create Control Implementation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /controls/{control_id}/implementations

   Create a new implementation for a control.

   **Request Body:**

   .. code-block:: json

      {
        "organization_id": "123e4567-e89b-12d3-a456-************",
        "status": "partially_implemented",
        "target_completion_date": "2024-03-01T00:00:00Z",
        "responsible_party": "IT Security Team",
        "evidence": [
          {
            "type": "document",
            "title": "Implementation Plan",
            "url": "https://example.com/plan.pdf"
          }
        ],
        "gaps": [
          {
            "description": "Missing automated monitoring",
            "priority": "high",
            "estimated_effort": 20.0
          }
        ],
        "remediation_plan": "Implement automated monitoring solution",
        "remediation_priority": "high",
        "implementation_cost": 10000.0,
        "effort_estimate": 80.0
      }

Update Control Implementation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:put:: /implementations/{implementation_id}

   Update an existing control implementation.

   **Path Parameters:**

   * ``implementation_id`` (UUID) - Implementation identifier

   **Request Body:** Partial implementation object with fields to update.

Assessment Management
--------------------

List Control Assessments
~~~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /controls/{control_id}/assessments

   Retrieve all assessments for a specific control.

   **Query Parameters:**

   * ``assessor`` (string, optional) - Filter by assessor name
   * ``assessment_type`` (string, optional) - Filter by assessment type
   * ``date_from`` (datetime, optional) - Filter assessments from date
   * ``date_to`` (datetime, optional) - Filter assessments to date
   * ``skip`` (integer, optional) - Number of records to skip
   * ``limit`` (integer, optional) - Maximum number of records to return

Create Control Assessment
~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /controls/{control_id}/assessments

   Create a new assessment for a control.

   **Request Body:**

   .. code-block:: json

      {
        "implementation_id": "123e4567-e89b-12d3-a456-************",
        "assessment_date": "2024-01-20T00:00:00Z",
        "assessor": "Internal Auditor",
        "assessment_type": "internal",
        "status": "implemented",
        "effectiveness_rating": "effective",
        "maturity_level": 4,
        "confidence_level": "high",
        "findings": [
          {
            "finding": "Control is well implemented",
            "severity": "info",
            "category": "positive"
          }
        ],
        "recommendations": [
          {
            "recommendation": "Consider automation opportunities",
            "priority": "medium",
            "estimated_effort": 10.0
          }
        ],
        "compliance_score": 95.0,
        "risk_score": 5.0,
        "next_assessment_date": "2024-07-20T00:00:00Z",
        "assessment_frequency": "semi-annual"
      }

Advanced Search
--------------

Framework Search
~~~~~~~~~~~~~~~

.. http:post:: /search

   Perform advanced search across frameworks, domains, categories, and controls.

   **Request Body:**

   .. code-block:: json

      {
        "query": "asset inventory",
        "framework_types": ["nist_csf_2_0", "iso_27001_2022"],
        "domains": ["ID", "A.8"],
        "control_types": ["inventory", "technical"],
        "risk_levels": ["high", "critical"],
        "automation_levels": ["automated", "semi_automated"],
        "implementation_status": ["implemented", "partially_implemented"],
        "tags": ["assets", "inventory"],
        "page": 1,
        "size": 20,
        "sort_by": "name",
        "sort_order": "asc"
      }

   **Response:**

   .. code-block:: json

      {
        "total": 45,
        "page": 1,
        "size": 20,
        "pages": 3,
        "results": [
          {
            "id": "123e4567-e89b-12d3-a456-************",
            "control_id": "ID.AM-01",
            "name": "Asset Inventory",
            "framework_name": "NIST CSF 2.0",
            "framework_type": "nist_csf_2_0",
            "risk_rating": "high",
            "automation_level": "automated"
          }
        ]
      }

Analytics and Reporting
----------------------

Compliance Gap Analysis
~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /analytics/gap-analysis

   Perform compliance gap analysis across frameworks.

   **Request Body:**

   .. code-block:: json

      {
        "framework_ids": [
          "123e4567-e89b-12d3-a456-************"
        ],
        "date_from": "2024-01-01T00:00:00Z",
        "date_to": "2024-12-31T23:59:59Z",
        "metrics": ["compliance_percentage", "risk_distribution"]
      }

   **Response:**

   .. code-block:: json

      [
        {
          "framework_id": "123e4567-e89b-12d3-a456-************",
          "framework_name": "NIST CSF 2.0",
          "total_controls": 108,
          "implemented_controls": 75,
          "partially_implemented_controls": 20,
          "not_implemented_controls": 13,
          "compliance_percentage": 69.4,
          "critical_gaps": [
            {
              "control_id": "PR.AC-01",
              "name": "Access Control Policy",
              "risk_rating": "critical",
              "status": "not_implemented",
              "gaps": ["No formal policy exists"]
            }
          ],
          "high_priority_gaps": [],
          "recommendations": [
            "Address critical access control gaps immediately",
            "Focus on protective controls to improve security posture"
          ]
        }
      ]

Compliance Dashboard
~~~~~~~~~~~~~~~~~~~

.. http:get:: /analytics/dashboard

   Get compliance dashboard data with key metrics and trends.

   **Query Parameters:**

   * ``framework_types`` (array, optional) - Filter by framework types

   **Response:**

   .. code-block:: json

      {
        "summary": {
          "total_frameworks": 4,
          "total_controls": 350,
          "implemented_controls": 245,
          "average_compliance": 70.0
        },
        "framework_breakdown": [
          {
            "framework_name": "NIST CSF 2.0",
            "framework_type": "nist_csf_2_0",
            "total_controls": 108,
            "implemented_controls": 75,
            "compliance_percentage": 69.4
          }
        ],
        "risk_distribution": {
          "critical": 15,
          "high": 45,
          "medium": 120,
          "low": 170
        },
        "recent_assessments": [
          {
            "assessment_id": "123e4567-e89b-12d3-a456-************",
            "control_id": "ID.AM-01",
            "assessor": "Internal Auditor",
            "assessment_date": "2024-01-20T00:00:00Z",
            "status": "implemented",
            "compliance_score": 95.0
          }
        ]
      }

Framework Mapping
----------------

Cross-Framework Mapping
~~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /mapping/cross-framework

   Get cross-framework control mappings between two frameworks.

   **Query Parameters:**

   * ``source_framework_id`` (UUID) - Source framework identifier
   * ``target_framework_id`` (UUID) - Target framework identifier

   **Response:**

   .. code-block:: json

      {
        "source_framework": {
          "id": "123e4567-e89b-12d3-a456-************",
          "name": "NIST CSF 2.0",
          "type": "nist_csf_2_0"
        },
        "target_framework": {
          "id": "123e4567-e89b-12d3-a456-************",
          "name": "ISO/IEC 27001:2022",
          "type": "iso_27001_2022"
        },
        "mappings": [
          {
            "source_control_id": "ID.AM-01",
            "source_control_name": "Asset Inventory",
            "potential_matches": [
              {
                "target_control_id": "A.8.1.1",
                "target_control_name": "Inventory of Assets",
                "similarity_score": 0.95,
                "mapping_rationale": "Both controls focus on asset inventory management"
              }
            ]
          }
        ],
        "mapping_metadata": {
          "generated_at": "2024-01-20T12:00:00Z",
          "total_source_controls": 108,
          "total_target_controls": 93,
          "mapping_algorithm": "keyword_similarity_v1"
        }
      }

Error Handling
--------------

The API uses standard HTTP status codes and returns error details in JSON format:

.. code-block:: json

   {
     "detail": "Framework not found",
     "error_code": "FRAMEWORK_NOT_FOUND",
     "timestamp": "2024-01-20T12:00:00Z"
   }

Common HTTP Status Codes:

* ``200`` - Success
* ``201`` - Created
* ``204`` - No Content
* ``400`` - Bad Request
* ``401`` - Unauthorized
* ``403`` - Forbidden
* ``404`` - Not Found
* ``409`` - Conflict
* ``422`` - Validation Error
* ``500`` - Internal Server Error

Rate Limiting
-------------

API requests are rate limited to:

* **Standard Users**: 1000 requests per hour
* **Premium Users**: 5000 requests per hour
* **Enterprise Users**: 10000 requests per hour

Rate limit headers are included in responses:

.. code-block:: http

   X-RateLimit-Limit: 1000
   X-RateLimit-Remaining: 999
   X-RateLimit-Reset: 1640995200

SDK Examples
-----------

Python SDK
~~~~~~~~~~

.. code-block:: python

   from blast_radius_sdk import BlastRadiusClient

   client = BlastRadiusClient(
       base_url="https://api.blast-radius.com",
       api_key="your-api-key"
   )

   # List frameworks
   frameworks = client.frameworks.list(framework_type="nist_csf_2_0")

   # Create implementation
   implementation = client.implementations.create(
       control_id="123e4567-e89b-12d3-a456-************",
       data={
           "status": "implemented",
           "responsible_party": "Security Team",
           "evidence": [{"type": "document", "url": "policy.pdf"}]
       }
   )

   # Perform gap analysis
   gap_analysis = client.analytics.gap_analysis(
       framework_ids=["123e4567-e89b-12d3-a456-************"]
   )

JavaScript SDK
~~~~~~~~~~~~~~

.. code-block:: javascript

   import { BlastRadiusClient } from '@blast-radius/sdk';

   const client = new BlastRadiusClient({
     baseUrl: 'https://api.blast-radius.com',
     apiKey: 'your-api-key'
   });

   // Search controls
   const searchResults = await client.frameworks.search({
     query: 'asset inventory',
     framework_types: ['nist_csf_2_0'],
     risk_levels: ['high', 'critical']
   });

   // Get compliance dashboard
   const dashboard = await client.analytics.dashboard({
     framework_types: ['nist_csf_2_0', 'iso_27001_2022']
   });

For more examples and detailed SDK documentation, visit the `SDK Documentation <https://docs.blast-radius.com/sdk>`_.

Next Steps
----------

* :doc:`../user-guides/cybersecurity-frameworks` - User guide for framework management
* :doc:`../integration-guides/framework-integration` - Integration examples and patterns
* :doc:`../compliance-workflows/assessment-procedures` - Assessment and compliance workflows
